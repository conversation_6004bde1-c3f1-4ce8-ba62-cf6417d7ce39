<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Photo Frame Adaptation Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0a1628 0%, #1e293b 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .viewport-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .viewport-btn {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(6, 182, 212, 0.6));
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
        }
        
        .viewport-btn:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 1), rgba(6, 182, 212, 0.8));
            transform: translateY(-2px);
        }
        
        .viewport-btn.active {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.8), rgba(16, 185, 129, 0.6));
            border-color: rgba(34, 197, 94, 0.5);
        }
        
        .test-results {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 12px;
            padding: 16px;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        .info { color: #3b82f6; }
        
        /* Updated service image styles with mobile adaptation */
        .suz-service-image-container {
            position: relative;
            overflow: hidden;
            border-radius: 1rem;
            background: linear-gradient(135deg, rgba(10, 132, 255, 0.1), rgba(100, 210, 255, 0.05));
            border: 1px solid rgba(10, 132, 255, 0.2);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            box-shadow:
                0 8px 25px rgba(10, 132, 255, 0.1),
                0 4px 12px rgba(100, 210, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 280px;
            contain: layout style paint;
            transform: translateZ(0);
            margin-bottom: 20px;
        }
        
        .suz-service-image {
            width: auto;
            height: auto;
            max-width: 100%;
            max-height: 260px;
            object-fit: cover;
            object-position: center;
            border-radius: 1rem;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            filter: brightness(0.9) contrast(1.1) saturate(1.1);
            transform: translateZ(0);
            will-change: transform, filter;
            contain: layout style paint;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            display: block;
        }
        
        /* Mobile adaptive styles */
        @media (max-width: 768px) {
            .suz-service-image-container {
                /* Enhanced glass morphism for mobile */
                background: linear-gradient(135deg, rgba(10, 132, 255, 0.15), rgba(100, 210, 255, 0.08)) !important;
                border: 1px solid rgba(10, 132, 255, 0.25) !important;
                -webkit-backdrop-filter: blur(8px) !important;
                backdrop-filter: blur(8px) !important;
                border-radius: 0.75rem !important;
                
                /* Adaptive container sizing */
                aspect-ratio: 4/3 !important;
                height: auto !important;
                min-height: 180px !important;
                max-height: 280px !important;
                align-items: stretch !important;
                padding: 8px !important;
            }
            
            .suz-service-image {
                width: 100% !important;
                height: 100% !important;
                object-fit: cover !important;
                min-height: unset !important;
                border-radius: 0.5rem !important;
                filter: brightness(0.95) contrast(1.05) saturate(1.05) !important;
            }
        }
        
        /* Fallback for browsers that don't support aspect-ratio */
        @supports not (aspect-ratio: 4/3) {
            @media (max-width: 768px) {
                .suz-service-image-container {
                    height: 220px !important;
                    aspect-ratio: unset !important;
                }
                
                .suz-service-image {
                    height: auto !important;
                    max-height: 200px !important;
                }
            }
        }
        
        .test-images {
            display: grid;
            gap: 20px;
            margin-top: 20px;
        }
        
        .image-info {
            background: rgba(15, 23, 42, 0.6);
            border-radius: 8px;
            padding: 12px;
            margin-top: 10px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 Mobile Photo Frame Test</h1>
        
        <div class="viewport-controls">
            <button type="button" class="viewport-btn" onclick="setViewport(320)">320px</button>
            <button type="button" class="viewport-btn" onclick="setViewport(375)">375px</button>
            <button type="button" class="viewport-btn" onclick="setViewport(414)">414px</button>
            <button type="button" class="viewport-btn active" onclick="setViewport(768)">768px</button>
            <button type="button" class="viewport-btn" onclick="setViewport(1024)">Desktop</button>
            <button type="button" class="viewport-btn" onclick="runMobileTest()">🧪 Test</button>
        </div>
        
        <div class="test-results" id="testResults">
            <div class="info">Resize viewport and click "🧪 Test" to analyze photo frame adaptation.</div>
        </div>
        
        <div class="test-images">
            <div class="suz-service-image-container">
                <img class="suz-service-image" 
                     src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDQwMCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjQwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSIyMDAiIHk9IjEyMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkhvdGVsemltbWVycmVpbmlndW5nPC90ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiMzYjgyZjYiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDZiNmQ0Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K" 
                     alt="Landscape Image (4:3 ratio)"
                     onload="updateImageInfo(this, 'Landscape 4:3')">
                <div class="image-info" id="info1">Loading...</div>
            </div>
            
            <div class="suz-service-image-container">
                <img class="suz-service-image" 
                     src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjQwMCIgdmlld0JveD0iMCAwIDMwMCA0MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iNDAwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSIxNTAiIHk9IjIwMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkJ1ZXJvcmVpbmlndW5nPC90ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM4YjVjZjYiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDZiNmQ0Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K" 
                     alt="Portrait Image (3:4 ratio)"
                     onload="updateImageInfo(this, 'Portrait 3:4')">
                <div class="image-info" id="info2">Loading...</div>
            </div>
            
            <div class="suz-service-image-container">
                <img class="suz-service-image" 
                     src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDUwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MDAiIGhlaWdodD0iMjAwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSIyNTAiIHk9IjEwMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE2IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZlbnN0ZXJyZWluaWd1bmc8L3RleHQ+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50IiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj4KPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iIzEwYjk4MSIvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMwNmI2ZDQiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K" 
                     alt="Wide Image (5:2 ratio)"
                     onload="updateImageInfo(this, 'Wide 5:2')">
                <div class="image-info" id="info3">Loading...</div>
            </div>
        </div>
    </div>

    <script>
        function setViewport(width) {
            if (width === 1024) {
                document.body.style.maxWidth = '1024px';
                document.querySelector('.test-container').style.maxWidth = '800px';
            } else {
                document.body.style.maxWidth = width + 'px';
                document.querySelector('.test-container').style.maxWidth = (width - 40) + 'px';
            }
            
            // Update active button
            document.querySelectorAll('.viewport-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Auto-run test after viewport change
            setTimeout(runMobileTest, 200);
        }
        
        function updateImageInfo(img, type) {
            const container = img.closest('.suz-service-image-container');
            const infoDiv = container.querySelector('.image-info');
            const imgRect = img.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();
            
            infoDiv.innerHTML = `
                <div class="info">${type}</div>
                Natural: ${img.naturalWidth}×${img.naturalHeight}px<br>
                Displayed: ${Math.round(imgRect.width)}×${Math.round(imgRect.height)}px<br>
                Container: ${Math.round(containerRect.width)}×${Math.round(containerRect.height)}px<br>
                Aspect Ratio: ${(img.naturalWidth / img.naturalHeight).toFixed(2)}
            `;
        }
        
        function runMobileTest() {
            const results = document.getElementById('testResults');
            const images = document.querySelectorAll('.suz-service-image');
            const containers = document.querySelectorAll('.suz-service-image-container');
            
            const viewportWidth = Math.min(window.innerWidth, document.body.offsetWidth);
            const isMobile = viewportWidth <= 768;
            
            let output = `<div class="info">📱 Testing at ${viewportWidth}px viewport (${isMobile ? 'Mobile' : 'Desktop'} mode)</div>\n`;
            
            let adaptationScore = 0;
            let totalTests = 0;
            
            images.forEach((img, index) => {
                const container = containers[index];
                const imgRect = img.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                const computedStyle = getComputedStyle(container);
                
                totalTests += 3; // 3 tests per image
                
                output += `<div class="success">📸 Image ${index + 1}:</div>`;
                output += `   Container: ${Math.round(containerRect.width)}×${Math.round(containerRect.height)}px\n`;
                output += `   Image: ${Math.round(imgRect.width)}×${Math.round(imgRect.height)}px\n`;
                
                // Test 1: Container adaptation
                if (isMobile) {
                    const hasAspectRatio = computedStyle.aspectRatio !== 'auto';
                    const hasFlexibleHeight = computedStyle.height === 'auto' || computedStyle.height.includes('auto');
                    
                    if (hasAspectRatio || hasFlexibleHeight) {
                        output += `   <div class="success">✅ Container adapts to content</div>\n`;
                        adaptationScore++;
                    } else {
                        output += `   <div class="warning">⚠️ Container uses fixed height</div>\n`;
                    }
                } else {
                    adaptationScore++; // Desktop always passes
                }
                
                // Test 2: Image fills container properly
                const fillRatio = (imgRect.width * imgRect.height) / (containerRect.width * containerRect.height);
                if (fillRatio > 0.8) {
                    output += `   <div class="success">✅ Image fills container well (${Math.round(fillRatio * 100)}%)</div>\n`;
                    adaptationScore++;
                } else {
                    output += `   <div class="error">❌ Image doesn't fill container (${Math.round(fillRatio * 100)}%)</div>\n`;
                }
                
                // Test 3: No excessive empty space
                const containerAspect = containerRect.width / containerRect.height;
                const imageAspect = img.naturalWidth / img.naturalHeight;
                const aspectDiff = Math.abs(containerAspect - imageAspect);
                
                if (aspectDiff < 0.5 || isMobile) {
                    output += `   <div class="success">✅ Good aspect ratio match</div>\n`;
                    adaptationScore++;
                } else {
                    output += `   <div class="warning">⚠️ Aspect ratio mismatch (${aspectDiff.toFixed(2)})</div>\n`;
                }
                
                output += '\n';
            });
            
            // Overall score
            const scorePercentage = Math.round((adaptationScore / totalTests) * 100);
            
            if (scorePercentage >= 90) {
                output += `<div class="success">🎉 EXCELLENT: Photo frame adaptation score: ${scorePercentage}%</div>\n`;
            } else if (scorePercentage >= 70) {
                output += `<div class="warning">👍 GOOD: Photo frame adaptation score: ${scorePercentage}%</div>\n`;
            } else {
                output += `<div class="error">❌ NEEDS IMPROVEMENT: Photo frame adaptation score: ${scorePercentage}%</div>\n`;
            }
            
            // Browser support check
            const supportsAspectRatio = CSS.supports('aspect-ratio', '4/3');
            output += `\n<div class="info">🌐 Browser Support:</div>\n`;
            output += `   aspect-ratio: ${supportsAspectRatio ? '✅' : '❌'} ${supportsAspectRatio ? 'Supported' : 'Using fallback'}\n`;
            
            results.innerHTML = `<pre>${output}</pre>`;
            
            // Update individual image info
            images.forEach((img, index) => {
                updateImageInfo(img, ['Landscape 4:3', 'Portrait 3:4', 'Wide 5:2'][index]);
            });
        }
        
        // Auto-run test on load
        setTimeout(() => {
            runMobileTest();
        }, 500);
    </script>
</body>
</html>
