<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Zoom Independence Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #0a1628 0%, #1e293b 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .zoom-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .zoom-btn {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(6, 182, 212, 0.6));
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .zoom-btn:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 1), rgba(6, 182, 212, 0.8));
            transform: translateY(-2px);
        }
        
        .zoom-btn.active {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.8), rgba(16, 185, 129, 0.6));
            border-color: rgba(34, 197, 94, 0.5);
        }
        
        .test-results {
            background: rgba(15, 23, 42, 0.8);
            border-radius: 12px;
            padding: 16px;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 14px;
            line-height: 1.5;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        .info { color: #3b82f6; }
        
        /* Service image styles from the actual implementation */
        .suz-service-image-container {
            position: relative;
            overflow: hidden;
            border-radius: 1rem;
            background: linear-gradient(135deg, rgba(10, 132, 255, 0.1), rgba(100, 210, 255, 0.05));
            border: 1px solid rgba(10, 132, 255, 0.2);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            box-shadow:
                0 8px 25px rgba(10, 132, 255, 0.1),
                0 4px 12px rgba(100, 210, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 280px;
            contain: layout style paint;
            transform: translateZ(0);
            margin-bottom: 20px;
        }
        
        .suz-service-image {
            width: auto;
            height: auto;
            max-width: 100%;
            max-height: 260px;
            object-fit: cover;
            object-position: center;
            border-radius: 1rem;
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            filter: brightness(0.9) contrast(1.1) saturate(1.1);
            transform: translateZ(0);
            will-change: transform, filter;
            contain: layout style paint;
            image-rendering: -webkit-optimize-contrast;
            image-rendering: crisp-edges;
            display: block;
        }
        
        .suz-service-image:hover {
            filter: brightness(1) contrast(1.2) saturate(1.2);
            transform: translateZ(0) scale(1.02);
        }
        
        .suz-service-image-container:hover {
            transform: translateY(-4px);
            box-shadow:
                0 20px 40px rgba(10, 132, 255, 0.2),
                0 8px 20px rgba(100, 210, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border-color: rgba(10, 132, 255, 0.3);
        }
        
        .test-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        @media (max-width: 768px) {
            .suz-service-image-container {
                aspect-ratio: 4/3 !important;
                height: auto !important;
                min-height: 180px !important;
                max-height: 280px !important;
                align-items: stretch !important;
                padding: 8px !important;
            }

            .suz-service-image {
                width: 100% !important;
                height: 100% !important;
                object-fit: cover !important;
                min-height: unset !important;
                border-radius: 0.5rem !important;
            }
        }

        /* Fallback for browsers that don't support aspect-ratio */
        @supports not (aspect-ratio: 4/3) {
            @media (max-width: 768px) {
                .suz-service-image-container {
                    height: 220px !important;
                    aspect-ratio: unset !important;
                }

                .suz-service-image {
                    height: auto !important;
                    max-height: 200px !important;
                }
            }
        }
        
        @media (min-width: 769px) and (max-width: 1024px) {
            .suz-service-image-container {
                height: 250px !important;
            }
            
            .suz-service-image {
                max-height: 230px !important;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Quick Zoom Independence Test</h1>
        
        <div class="zoom-controls">
            <button type="button" class="zoom-btn" onclick="setZoom(0.5)">50%</button>
            <button type="button" class="zoom-btn" onclick="setZoom(0.75)">75%</button>
            <button type="button" class="zoom-btn active" onclick="setZoom(1.0)">100%</button>
            <button type="button" class="zoom-btn" onclick="setZoom(1.25)">125%</button>
            <button type="button" class="zoom-btn" onclick="setZoom(1.5)">150%</button>
            <button type="button" class="zoom-btn" onclick="setZoom(2.0)">200%</button>
            <button type="button" class="zoom-btn" onclick="runQuickTest()">🧪 Test</button>
        </div>
        
        <div class="test-results" id="testResults">
            <div class="info">Click zoom buttons to test, then click "🧪 Test" to measure dimensions.</div>
        </div>
        
        <div class="test-images">
            <div class="suz-service-image-container">
                <img class="suz-service-image" 
                     src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI0MCIgdmlld0JveD0iMCAwIDQwMCAyNDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMjQwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSIyMDAiIHk9IjEyMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkhvdGVsemltbWVycmVpbmlndW5nPC90ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiMzYjgyZjYiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDZiNmQ0Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K" 
                     alt="Hotelzimmerreinigung Test">
            </div>
            
            <div class="suz-service-image-container">
                <img class="suz-service-image" 
                     src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSJ1cmwoI2dyYWRpZW50KSIvPgo8dGV4dCB4PSIyMDAiIHk9IjE1MCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjE4IiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkJ1ZXJvcmVpbmlndW5nPC90ZXh0Pgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudCIgeDE9IjAlIiB5MT0iMCUiIHgyPSIxMDAlIiB5Mj0iMTAwJSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM4YjVjZjYiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjMDZiNmQ0Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K" 
                     alt="Bueroreinigung Test">
            </div>
        </div>
    </div>

    <script>
        let testData = {};
        
        function setZoom(level) {
            document.body.style.zoom = level;
            
            // Update active button
            document.querySelectorAll('.zoom-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
        }
        
        function runQuickTest() {
            const results = document.getElementById('testResults');
            const images = document.querySelectorAll('.suz-service-image');
            const containers = document.querySelectorAll('.suz-service-image-container');
            
            const currentZoom = parseFloat(document.body.style.zoom) || 1.0;
            const zoomPercent = Math.round(currentZoom * 100);
            
            let output = `<div class="info">🔍 Testing at ${zoomPercent}% zoom level</div>\n`;
            
            images.forEach((img, index) => {
                const container = containers[index];
                const imgRect = img.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();
                
                output += `<div class="success">📸 Image ${index + 1}:</div>`;
                output += `   Container: ${Math.round(containerRect.width)}×${Math.round(containerRect.height)}px\n`;
                output += `   Image: ${Math.round(imgRect.width)}×${Math.round(imgRect.height)}px\n`;
                output += `   Natural: ${img.naturalWidth}×${img.naturalHeight}px\n\n`;
            });
            
            // Store for comparison
            testData[zoomPercent] = {
                containers: Array.from(containers).map(c => {
                    const rect = c.getBoundingClientRect();
                    return { width: Math.round(rect.width), height: Math.round(rect.height) };
                }),
                images: Array.from(images).map(img => {
                    const rect = img.getBoundingClientRect();
                    return { width: Math.round(rect.width), height: Math.round(rect.height) };
                })
            };
            
            // Compare with 100% if available
            if (testData[100] && zoomPercent !== 100) {
                const base = testData[100];
                const current = testData[zoomPercent];
                
                output += `<div class="info">📊 Comparison with 100%:</div>\n`;
                
                let allConsistent = true;
                base.containers.forEach((baseContainer, index) => {
                    const currentContainer = current.containers[index];
                    const widthDiff = Math.abs(baseContainer.width - currentContainer.width);
                    const heightDiff = Math.abs(baseContainer.height - currentContainer.height);
                    
                    if (widthDiff <= 2 && heightDiff <= 2) {
                        output += `   <div class="success">✅ Container ${index + 1}: Consistent (±${Math.max(widthDiff, heightDiff)}px)</div>\n`;
                    } else {
                        output += `   <div class="error">❌ Container ${index + 1}: Size changed (±${Math.max(widthDiff, heightDiff)}px)</div>\n`;
                        allConsistent = false;
                    }
                });
                
                if (allConsistent) {
                    output += `<div class="success">🎉 SUCCESS: Zoom-independent sizing confirmed!</div>\n`;
                }
            }
            
            results.innerHTML = `<pre>${output}</pre>`;
        }
        
        // Auto-test at 100%
        setTimeout(() => {
            runQuickTest();
        }, 500);
    </script>
</body>
</html>
