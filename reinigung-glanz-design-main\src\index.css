
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple-inspired design system variables */
@layer base {
  :root {
    /* Apple-Inspired Design System - SUZ Reinigung */

    /* Primary Brand Colors - Dark Theme (Now Default) */
    --suz-blue-primary: #0A84FF;
    --suz-blue-secondary: #64D2FF;
    --suz-blue-tertiary: #30D158;

    /* Dark Neutral Foundation */
    --suz-gray-50: #1C1C1E;
    --suz-gray-100: #2C2C2E;
    --suz-gray-200: #3A3A3C;
    --suz-gray-300: #48484A;
    --suz-gray-400: #636366;
    --suz-gray-500: #8E8E93;
    --suz-gray-600: #AEAEB2;
    --suz-gray-700: #C7C7CC;
    --suz-gray-800: #D1D1D6;
    --suz-gray-900: #F2F2F7;

    /* Dark Semantic Colors */
    --suz-success: #30D158;
    --suz-warning: #FF9F0A;
    --suz-error: #FF453A;
    --suz-info: #0A84FF;

    /* Dark Surface Colors */
    --suz-surface-primary: #000000;
    --suz-surface-secondary: #1C1C1E;
    --suz-surface-tertiary: #2C2C2E;
    --suz-surface-glass: rgba(28, 28, 30, 0.8);

    /* Typography Scale */
    --text-display-xl: 4.5rem;    /* 72px - Hero headlines */
    --text-display-lg: 3.75rem;   /* 60px - Section headers */
    --text-display-md: 3rem;      /* 48px - Page titles */
    --text-display-sm: 2.25rem;   /* 36px - Card titles */

    --text-heading-xl: 1.875rem;  /* 30px - H1 */
    --text-heading-lg: 1.5rem;    /* 24px - H2 */
    --text-heading-md: 1.25rem;   /* 20px - H3 */
    --text-heading-sm: 1.125rem;  /* 18px - H4 */

    --text-body-xl: 1.125rem;     /* 18px - Large body */
    --text-body-lg: 1rem;         /* 16px - Default body */
    --text-body-md: 0.875rem;     /* 14px - Small body */
    --text-body-sm: 0.75rem;      /* 12px - Caption */

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-regular: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Spacing Scale (8px base unit) */
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */
    --space-20: 5rem;     /* 80px */
    --space-24: 6rem;     /* 96px */
    --space-32: 8rem;     /* 128px */

    /* Layout Spacing - Enhanced for consistent visual hierarchy */
    --section-padding-sm: var(--space-16);   /* 64px - Small sections */
    --section-padding-md: var(--space-20);   /* 80px - Medium sections */
    --section-padding-lg: var(--space-24);   /* 96px - Large sections */
    --section-padding-xl: var(--space-32);   /* 128px - Extra large sections */

    /* Standardized section spacing for consistent layout */
    --section-spacing-vertical: var(--space-24);  /* 96px - Standard vertical spacing between sections */
    --section-spacing-mobile: var(--space-16);    /* 64px - Mobile vertical spacing */

    --component-padding-sm: var(--space-4);
    --component-padding-md: var(--space-6);
    --component-padding-lg: var(--space-8);

    /* Border Radius System */
    --radius-none: 0;
    --radius-sm: 0.25rem;    /* 4px */
    --radius-md: 0.5rem;     /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */
    --radius-2xl: 1.5rem;    /* 24px */
    --radius-3xl: 2rem;      /* 32px */
    --radius-full: 9999px;

    /* Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-blue: 0 10px 25px -5px rgba(0, 122, 255, 0.15);
    --shadow-green: 0 10px 25px -5px rgba(52, 199, 89, 0.15);

    /* Animation Timing */
    --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
    --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
    --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --duration-slow: 350ms;
    --duration-slower: 500ms;

    /* Legacy Shadcn/UI Variables (Dark Theme - for compatibility) */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
    --radius: 0.5rem;
  }


}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
    font-weight: 400;
  }

  body.force-apple-design {
    font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Apple-inspired component styles */
@layer components {

/* Premium Background Gradient - Dark Theme Default */
.bg-premium-gradient {
  background: linear-gradient(135deg,
    #000000 0%,
    #1a1a1a 25%,
    #0f172a 50%,
    #1e293b 75%,
    #0f1419 100%) !important;
  min-height: 100vh !important;
  position: relative;
}

.bg-premium-gradient::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(10, 132, 255, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(100, 210, 255, 0.06) 0%, transparent 50%);
  pointer-events: none;
}

/* Force Apple Design System Priority */
body.force-apple-design,
.force-apple-design {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
}

.force-apple-design .bg-premium-gradient {
  background: linear-gradient(135deg,
    #000000 0%,
    #1a1a1a 25%,
    #0f172a 50%,
    #1e293b 75%,
    #0f1419 100%) !important;
}

/* Enhanced Glass Morphism Effects with Consistent Performance */
.glass-morphism {
  background: rgba(28, 28, 30, 0.15);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px 0 rgba(10, 132, 255, 0.15);
  /* Performance optimization */
  will-change: transform, opacity;
  contain: layout style paint;
  /* Fallback for browsers without backdrop-filter support */
  background-image: linear-gradient(135deg,
    rgba(28, 28, 30, 0.85) 0%,
    rgba(44, 44, 46, 0.8) 50%,
    rgba(28, 28, 30, 0.85) 100%);
}

.glass-morphism-premium {
  background: rgba(28, 28, 30, 0.4) !important;
  -webkit-backdrop-filter: blur(30px) !important;
  backdrop-filter: blur(30px) !important;
  box-shadow:
    0 20px 40px -10px rgba(10, 132, 255, 0.15),
    0 10px 25px -5px rgba(100, 210, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  /* Performance optimization */
  will-change: transform, opacity;
  contain: layout style paint;
  /* Enhanced fallback for browsers without backdrop-filter support */
  background-image: linear-gradient(135deg,
    rgba(28, 28, 30, 0.9) 0%,
    rgba(44, 44, 46, 0.85) 25%,
    rgba(58, 58, 60, 0.9) 50%,
    rgba(44, 44, 46, 0.85) 75%,
    rgba(28, 28, 30, 0.9) 100%) !important;
}

/* Progressive enhancement for browsers with backdrop-filter support */
@supports (backdrop-filter: blur(20px)) or (-webkit-backdrop-filter: blur(20px)) {
  .glass-morphism {
    background: rgba(28, 28, 30, 0.15);
    background-image: none;
  }

  .glass-morphism-premium {
    background: rgba(28, 28, 30, 0.4) !important;
    background-image: none !important;
  }
}

/* Enhanced Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #0ea5e9, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  background-size: 200% 200%;
}

.gradient-text-animated {
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #0ea5e9, #8b5cf6, #3b82f6) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  background-size: 300% 300% !important;
  animation: gradient-shift 6s ease-in-out infinite !important;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Pulse Glow Effect */
.pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    text-shadow: 0 0 30px rgba(59, 130, 246, 0.5), 0 0 40px rgba(6, 182, 212, 0.3);
  }
}

/* Logo Glow Effect */
.logo-glow:hover {
  box-shadow: 
    0 0 30px rgba(59, 130, 246, 0.4),
    0 0 60px rgba(6, 182, 212, 0.2),
    0 20px 40px -10px rgba(59, 130, 246, 0.15);
}

/* Premium Button Effects */
.premium-button {
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 10px 30px -5px rgba(0, 0, 0, 0.25),
    0 0 20px rgba(255, 255, 255, 0.2) inset;
}

.premium-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.premium-button:hover::before {
  left: 100%;
}

.premium-button-3d {
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 15px 35px -5px rgba(0, 0, 0, 0.2),
    0 5px 15px -3px rgba(0, 0, 0, 0.1),
    0 0 20px rgba(255, 255, 255, 0.3) inset;
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-button-3d:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 20px 40px -5px rgba(0, 0, 0, 0.25),
    0 8px 20px -3px rgba(0, 0, 0, 0.15),
    0 0 30px rgba(255, 255, 255, 0.4) inset;
}

/* Enhanced Service Card Hover Effect */
.service-card-premium {
  position: relative;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card-premium::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.08), 
    rgba(6, 182, 212, 0.08),
    rgba(139, 92, 246, 0.05));
  opacity: 0;
  transition: opacity 0.4s ease;
  border-radius: inherit;
}

.service-card-premium:hover::before {
  opacity: 1;
}

.service-card-premium:hover {
  transform: translateY(-8px);
  box-shadow: 
    0 25px 50px -10px rgba(59, 130, 246, 0.15),
    0 10px 30px -5px rgba(6, 182, 212, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Icon Badge Style */
.icon-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.6), rgba(255, 255, 255, 0.2));
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.4);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  box-shadow:
    0 8px 25px rgba(59, 130, 246, 0.15),
    0 4px 12px rgba(6, 182, 212, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-badge:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 30px rgba(59, 130, 246, 0.2),
    0 6px 15px rgba(6, 182, 212, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Enhanced Icon Badge Style for Better Visibility */
.icon-badge-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 96px;
  height: 96px;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.15),
    rgba(6, 182, 212, 0.12),
    rgba(255, 255, 255, 0.8));
  border-radius: 50%;
  border: 2px solid rgba(59, 130, 246, 0.2);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  box-shadow:
    0 12px 35px rgba(59, 130, 246, 0.25),
    0 6px 18px rgba(6, 182, 212, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.9),
    inset 0 -2px 0 rgba(59, 130, 246, 0.1);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.icon-badge-enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.1),
    rgba(6, 182, 212, 0.08));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.icon-badge-enhanced:hover::before {
  opacity: 1;
}

.icon-badge-enhanced:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 20px 45px rgba(59, 130, 246, 0.35),
    0 10px 25px rgba(6, 182, 212, 0.25),
    inset 0 2px 0 rgba(255, 255, 255, 1),
    inset 0 -2px 0 rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.4);
}

/* Team Card Effects */
.team-card {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.team-card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 20px 40px -10px rgba(59, 130, 246, 0.12),
    0 8px 25px -5px rgba(6, 182, 212, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

/* Leadership Card Enhanced Styling */
.leadership-card {
  position: relative;
  overflow: hidden;
}

.leadership-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(59, 130, 246, 0.05),
    rgba(6, 182, 212, 0.03));
  border-radius: inherit;
  pointer-events: none;
}

.leadership-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 25px 50px -10px rgba(59, 130, 246, 0.2),
    0 12px 30px -5px rgba(6, 182, 212, 0.15),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Enhanced Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(2deg);
  }
  66% {
    transform: translateY(-10px) rotate(-1deg);
  }
}

.floating-element {
  animation: float 8s ease-in-out infinite;
}

/* Enhanced Fade In Animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 1s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Premium Micro-Interactions */
@keyframes subtle-bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

@keyframes gentle-scale {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-shimmer {
  animation: shimmer 1.5s ease-in-out;
}

@keyframes soft-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
  }
  50% {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.4), 0 0 40px rgba(6, 182, 212, 0.2);
  }
}

/* Enhanced Company Showcase Infinite Scroll Animation - 60fps optimized */
@keyframes scroll-right {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-50%, 0, 0);
  }
}

/* Webkit-specific animation for iOS Safari */
@-webkit-keyframes scroll-right {
  0% {
    -webkit-transform: translate3d(0, 0, 0);
  }
  100% {
    -webkit-transform: translate3d(-50%, 0, 0);
  }
}

/* WebKit-specific keyframes for iOS Safari compatibility */
@-webkit-keyframes scroll-right {
  0% {
    -webkit-transform: translateX(0) translateZ(0);
    transform: translateX(0) translateZ(0);
  }
  100% {
    -webkit-transform: translateX(-50%) translateZ(0);
    transform: translateX(-50%) translateZ(0);
  }
}

.animate-scroll-right {
  /* Mobile-specific optimizations - webkit prefixes first */
  -webkit-animation: scroll-right 60s linear infinite;
  -webkit-animation-fill-mode: both;
  -webkit-animation-play-state: running;
  -webkit-transform: translate3d(0, 0, 0);
  -webkit-backface-visibility: hidden;

  /* Standard properties */
  animation: scroll-right 60s linear infinite;
  animation-fill-mode: both;
  animation-play-state: running;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;

  /* Force hardware acceleration for smooth 60fps */
  will-change: transform;
  /* Ensure proper 3D context */
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* Enhanced Company Showcase Styles - Premium Dark Theme */
.suz-company-showcase {
  position: relative;
  /* Use the same premium gradient as the main website background */
  background: linear-gradient(135deg,
    #000000 0%,
    #1a1a1a 25%,
    #0f172a 50%,
    #1e293b 75%,
    #0f1419 100%) !important;
  padding: var(--space-20) 0 !important;
  /* Remove margins that create black strips between sections */
  margin: 0 !important;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* Add the same radial gradient overlay as the main bg-premium-gradient */
.suz-company-showcase::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(10, 132, 255, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(100, 210, 255, 0.06) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Ensure content appears above the overlay */
.suz-company-showcase > * {
  position: relative;
  z-index: 1;
}

.suz-company-scroll {
  /* Optimize for smooth 60fps animation */
  will-change: transform;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
  width: max-content;
  /* GPU acceleration for better performance */
  transform: translateZ(0);
  /* Smooth animation timing */
  animation-timing-function: linear;
  /* Ensure proper display on all devices */
  display: flex;
  flex-wrap: nowrap;
  /* Prevent layout shifts */
  min-height: fit-content;
  /* Enhanced mobile touch handling */
  touch-action: pan-y pinch-zoom;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  /* Prevent touch interference */
  pointer-events: auto;
}

.suz-company-card {
  /* Prevent layout shifts during animation */
  contain: layout style paint;
  transform: translateZ(0);
  min-width: 300px;
  max-width: 350px;
}

/* Enhanced company card content styling */
.suz-company-card-content {
  padding: var(--space-6) !important;
}

/* Company showcase icon styling */
.suz-company-showcase .icon-badge-enhanced {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.2), rgba(100, 210, 255, 0.1));
  border: 1px solid rgba(10, 132, 255, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.suz-company-showcase .icon-badge-enhanced:hover {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.3), rgba(100, 210, 255, 0.2));
  border: 1px solid rgba(10, 132, 255, 0.5);
  box-shadow: 0 10px 20px rgba(10, 132, 255, 0.2);
}

/* Services section icon styling - Premium glass morphism matching company showcase */
.suz-services-section .suz-icon-badge-premium {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  /* Base background for browsers without backdrop-filter support */
  background: rgba(28, 28, 30, 0.9);
  /* Enhanced glass morphism with progressive enhancement */
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.2), rgba(100, 210, 255, 0.1));
  border: 1px solid rgba(10, 132, 255, 0.3);
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  box-shadow:
    0 8px 25px rgba(10, 132, 255, 0.15),
    0 4px 12px rgba(100, 210, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  /* Hardware acceleration for better performance */
  will-change: transform, box-shadow;
  /* Optimize for 60fps animations */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  perspective: 1000px;
  /* Force GPU acceleration */
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}

.suz-services-section .suz-icon-badge-premium::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(10, 132, 255, 0.1),
    rgba(100, 210, 255, 0.08));
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.suz-services-section .suz-icon-badge-premium:hover::before {
  opacity: 1;
}

.suz-services-section .suz-icon-badge-premium:hover {
  -webkit-transform: translateY(-3px) scale(1.03) translateZ(0);
  -moz-transform: translateY(-3px) scale(1.03) translateZ(0);
  -ms-transform: translateY(-3px) scale(1.03) translateZ(0);
  -o-transform: translateY(-3px) scale(1.03) translateZ(0);
  transform: translateY(-3px) scale(1.03) translateZ(0);
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.3), rgba(100, 210, 255, 0.2));
  border: 1px solid rgba(10, 132, 255, 0.5);
  box-shadow:
    0 15px 35px rgba(10, 132, 255, 0.25),
    0 8px 20px rgba(100, 210, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Services section icon SVG styling */
.suz-services-section .suz-icon-badge-premium svg {
  width: 36px;
  height: 36px;
  color: #60a5fa;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.suz-services-section .suz-icon-badge-premium:hover svg {
  color: #93c5fd;
  transform: scale(1.05);
  filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));
}

/* Services section accessibility features */
.suz-services-section .suz-icon-badge-premium:focus {
  outline: 2px solid rgba(59, 130, 246, 0.8);
  outline-offset: 2px;
  box-shadow:
    0 15px 35px rgba(10, 132, 255, 0.25),
    0 8px 20px rgba(100, 210, 255, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    0 0 0 2px rgba(59, 130, 246, 0.3);
}

/* Service Images - Premium glass morphism styling with true zoom-independent sizing */
.suz-service-image-container {
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.1), rgba(100, 210, 255, 0.05));
  border: 1px solid rgba(10, 132, 255, 0.2);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  box-shadow:
    0 8px 25px rgba(10, 132, 255, 0.1),
    0 4px 12px rgba(100, 210, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  /* True zoom-independent container sizing using fixed pixel dimensions */
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  /* Fixed pixel dimensions that remain constant regardless of browser zoom */
  height: 280px; /* Fixed height for desktop */
  /* Additional zoom-independent properties */
  contain: layout style paint;
  /* Force hardware acceleration for smooth zoom-independent rendering */
  transform: translateZ(0);
}

.suz-service-image-container:hover {
  transform: translateY(-4px);
  box-shadow:
    0 20px 40px rgba(10, 132, 255, 0.2),
    0 8px 20px rgba(100, 210, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(10, 132, 255, 0.3);
}

.suz-service-image {
  /* True zoom-independent image sizing using fixed pixel dimensions */
  width: auto;
  height: auto;
  /* Fixed maximum dimensions that remain constant regardless of browser zoom */
  max-width: 100%;
  max-height: 260px; /* Fixed max height for desktop */
  /* Use cover to fill container without letterboxing, but maintain aspect ratio */
  object-fit: cover;
  object-position: center;
  border-radius: 1rem;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(0.9) contrast(1.1) saturate(1.1);
  /* Force hardware acceleration for smooth zoom-independent rendering */
  transform: translateZ(0);
  will-change: transform, filter;
  /* Additional zoom-independent properties */
  contain: layout style paint;
  /* Optimize image rendering for consistent display */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  /* Ensure proper scaling behavior */
  display: block;
}

.suz-service-image:hover {
  filter: brightness(1) contrast(1.2) saturate(1.2);
  transform: translateZ(0) scale(1.02);
}

.suz-service-image-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(10, 132, 255, 0.1) 50%,
    transparent 100%
  );
  transition: opacity 0.3s ease;
}

/* Cross-browser zoom-independent service image fallbacks */
@supports not (display: flex) {
  /* Fallback for very old browsers that don't support flexbox */
  .suz-service-image-container {
    display: block !important;
    text-align: center !important;
    height: 280px !important;
  }

  .suz-service-image {
    display: inline-block !important;
    vertical-align: middle !important;
    max-height: 260px !important;
  }

  @media (max-width: 768px) {
    .suz-service-image-container {
      height: 220px !important;
    }

    .suz-service-image {
      max-height: 200px !important;
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .suz-service-image-container {
      height: 250px !important;
    }

    .suz-service-image {
      max-height: 230px !important;
    }
  }
}

/* Service image loading states */
.suz-service-image[data-optimize="true"] {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.1), rgba(100, 210, 255, 0.05));
  background-size: 400% 400%;
  animation: shimmer-loading 2s ease-in-out infinite;
}

@keyframes shimmer-loading {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.suz-services-section .suz-icon-badge-premium:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.8);
  outline-offset: 2px;
}

/* Enhanced reduced motion support for services icons */
@media (prefers-reduced-motion: reduce) {
  .suz-services-section .suz-icon-badge-premium,
  .suz-services-section .suz-icon-badge-premium::before,
  .suz-services-section .suz-icon-badge-premium svg {
    transition: none !important;
    animation: none !important;
    /* Disable hardware acceleration when motion is reduced */
    will-change: auto !important;
    transform: none !important;
  }

  .suz-services-section .suz-icon-badge-premium:hover {
    transform: none !important;
    /* Use subtle color changes instead of transforms */
    background: rgba(10, 132, 255, 0.2) !important;
    border-color: rgba(10, 132, 255, 0.4) !important;
  }

  .suz-services-section .suz-icon-badge-premium:hover svg {
    transform: none !important;
    /* Use color change instead of scale */
    color: #93c5fd !important;
  }

  /* Disable all performance optimizations when motion is reduced */
  .performance-animation,
  .suz-interactive-element {
    will-change: auto !important;
    transform: none !important;
    backface-visibility: visible !important;
    perspective: none !important;
    contain: none !important;
    transition: color 0.2s ease, background-color 0.2s ease !important;
  }

  .suz-interactive-element:hover,
  .suz-interactive-element:focus {
    transform: none !important;
    filter: none !important;
    /* Use subtle color changes for accessibility */
    background-color: rgba(59, 130, 246, 0.1) !important;
  }
}

.suz-company-card-content {
  padding: var(--component-padding-md);
}

/* Pause animation on hover for better UX - desktop only */
@media (min-width: 769px) {
  .suz-company-showcase:hover .animate-scroll-right {
    animation-play-state: paused;
  }
}

/* Ensure animation continues on mobile/touch devices */
@media (max-width: 768px) {
  .suz-company-showcase .animate-scroll-right {
    /* Webkit prefixes first for iOS Safari */
    -webkit-animation-play-state: running !important;
    -webkit-backface-visibility: hidden;

    /* Standard properties */
    animation-play-state: running !important;
    backface-visibility: hidden;

    /* Enhanced mobile performance optimizations */
    will-change: transform;
    transform-style: preserve-3d;
    perspective: 1000px;
    /* Prevent touch interference with animation */
    touch-action: pan-y pinch-zoom;
    /* Optimize for mobile GPU */
    contain: layout style paint;
  }
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-scroll-right {
    animation: none !important;
    animation-play-state: paused !important;
  }

  .suz-company-scroll {
    overflow-x: auto;
    scroll-behavior: smooth;
    /* Provide horizontal scrolling fallback */
    scrollbar-width: thin;
    scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
  }

  .suz-company-scroll::-webkit-scrollbar {
    height: 4px;
  }

  .suz-company-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .suz-company-scroll::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 2px;
  }

  /* Enhanced accessibility for reduced motion */
  .suz-company-showcase .suz-card-glass {
    -webkit-transition: none !important;
    transition: none !important;
  }

  /* Enhanced focus indicators for keyboard navigation */
  .suz-company-card:focus-within,
  .suz-company-card:focus {
    outline: 3px solid #3b82f6 !important;
    outline-offset: 2px !important;
    box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.2) !important;
  }

  /* Ensure all content is accessible in static layout */
  .suz-company-card {
    opacity: 1 !important;
    -webkit-transform: none !important;
    transform: none !important;
  }
}

/* Enhanced Company Showcase Responsive Design */
@media (max-width: 768px) {
  .suz-company-showcase {
    /* Maintain the same premium gradient background on mobile */
    background: linear-gradient(135deg,
      #000000 0%,
      #1a1a1a 25%,
      #0f172a 50%,
      #1e293b 75%,
      #0f1419 100%) !important;
    padding: var(--space-16) 0 !important;
    /* Remove margins that create black strips between sections */
    margin: 0 !important;
    /* Ensure proper overflow handling on mobile */
    overflow-x: hidden;
    /* Optimize for mobile performance */
    contain: layout style;
  }

  .suz-company-card {
    min-width: 260px;
    max-width: 300px;
    /* Ensure cards maintain proper spacing on mobile */
    flex-shrink: 0;
    /* Optimize mobile card performance */
    contain: layout style paint;
    /* Improve touch targets */
    min-height: 200px;
  }

  .animate-scroll-right {
    /* Webkit prefixes first for iOS Safari */
    -webkit-animation-duration: 45s;
    -webkit-animation-play-state: running !important;

    /* Standard properties */
    animation-duration: 45s; /* Faster on mobile for better UX */
    animation-play-state: running !important;

    /* Ensure proper transform origin */
    transform-origin: left center;
    /* Mobile-specific performance optimizations */
    will-change: transform;
  }

  .suz-company-scroll {
    /* Ensure proper width calculation on mobile */
    width: max-content;
    /* Allow touch events but prevent scrolling interference */
    touch-action: pan-y pinch-zoom;
    /* Mobile performance optimizations */
    will-change: transform;
    contain: layout style;
    /* Ensure proper 3D context for mobile */
    transform-style: preserve-3d;
    perspective: 1000px;
  }

  /* Adjust fade gradients for mobile */
  .suz-company-showcase .absolute.w-32 {
    width: 4rem; /* Smaller fade areas on mobile */
  }

  /* Improve touch interaction on mobile */
  .suz-company-showcase .suz-card-glass {
    /* Enhanced touch targets for mobile */
    min-height: 200px;
    cursor: default; /* Remove pointer cursor on mobile */
    /* Optimize touch performance */
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(10, 132, 255, 0.1);
  }

  .suz-company-showcase .suz-card-glass:hover {
    transform: none !important; /* Disable hover effects on mobile */
    background: rgba(28, 28, 30, 0.9) !important;
    box-shadow:
      0 20px 40px -10px rgba(0, 0, 0, 0.6),
      0 10px 25px -5px rgba(10, 132, 255, 0.1),
      0 2px 8px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  }

  /* Mobile-specific touch feedback */
  .suz-company-showcase .suz-card-glass:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .suz-company-showcase {
    /* Maintain the same premium gradient background on small mobile */
    background: linear-gradient(135deg,
      #000000 0%,
      #1a1a1a 25%,
      #0f172a 50%,
      #1e293b 75%,
      #0f1419 100%) !important;
    overflow-x: hidden !important;
    /* Reduce padding for small screens */
    padding: var(--space-12) 0 !important;
  }

  .suz-company-card {
    min-width: 240px;
    max-width: 280px;
    /* Adjust height for small screens */
    min-height: 180px;
  }

  .animate-scroll-right {
    /* Webkit prefixes first for iOS Safari */
    -webkit-animation-duration: 40s;
    -webkit-animation-play-state: running !important;

    /* Standard properties */
    animation-duration: 40s;
    animation-play-state: running !important;

    /* Optimize for small screens */
    will-change: transform;
    transform-origin: left center;
  }

  .suz-company-scroll {
    /* Force proper width on very small screens */
    width: max-content !important;
    /* Enhanced small screen optimizations */
    will-change: transform;
    contain: layout style;
    transform-style: preserve-3d;
    perspective: 1000px;
    /* Improve touch handling on small screens */
    touch-action: pan-y pinch-zoom;
  }

  /* Adjust fade gradients for small screens */
  .suz-company-showcase .absolute.w-32 {
    width: 2rem !important; /* Even smaller fade areas on small mobile */
  }

  /* Improve typography readability on small screens */
  .suz-company-card-content h3 {
    font-size: 0.9rem !important;
    line-height: 1.3 !important;
    /* Enhanced readability for German business names */
    font-weight: 600 !important;
    color: #f8fafc !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
    /* Prevent text overflow */
    word-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto;
  }

  /* Improve icon visibility on small screens */
  .suz-company-showcase .icon-badge-enhanced {
    width: 50px !important;
    height: 50px !important;
    /* Enhanced contrast for mobile */
    background: linear-gradient(135deg, rgba(10, 132, 255, 0.3), rgba(100, 210, 255, 0.2)) !important;
    border: 1px solid rgba(10, 132, 255, 0.4) !important;
  }

  .suz-company-showcase .icon-badge-enhanced svg {
    width: 24px !important;
    height: 24px !important;
    color: #60a5fa !important;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }

  /* Services section mobile icon styling */
  .suz-services-section .suz-icon-badge-premium {
    width: 70px !important;
    height: 70px !important;
    /* Enhanced contrast for mobile readability */
    background: linear-gradient(135deg, rgba(10, 132, 255, 0.3), rgba(100, 210, 255, 0.2)) !important;
    border: 1px solid rgba(10, 132, 255, 0.4) !important;
    -webkit-backdrop-filter: blur(12px) !important;
    backdrop-filter: blur(12px) !important;
  }

  .suz-services-section .suz-icon-badge-premium svg {
    width: 30px !important;
    height: 30px !important;
    color: #60a5fa !important;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  }

  .suz-services-section .suz-icon-badge-premium:hover {
    transform: translateY(-2px) scale(1.02) !important;
  }

  /* Service images mobile optimization with adaptive container sizing */
  .suz-service-image-container {
    margin-bottom: 1rem !important;
    border-radius: 0.75rem !important;
    /* Enhanced glass morphism for mobile */
    background: linear-gradient(135deg, rgba(10, 132, 255, 0.15), rgba(100, 210, 255, 0.08)) !important;
    border: 1px solid rgba(10, 132, 255, 0.25) !important;
    -webkit-backdrop-filter: blur(8px) !important;
    backdrop-filter: blur(8px) !important;
    /* Adaptive container sizing for mobile - adjusts to image content */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 100% !important;
    /* Flexible height that adapts to image aspect ratio on mobile */
    min-height: 180px !important; /* Minimum height for mobile */
    max-height: 280px !important; /* Maximum height for mobile */
    height: auto !important; /* Allow container to adapt to image */
    /* Additional zoom-independent properties for mobile */
    contain: layout style paint !important;
    /* Force hardware acceleration for smooth zoom-independent rendering */
    transform: translateZ(0) !important;
  }

  .suz-service-image {
    /* Adaptive image sizing for mobile - maintains aspect ratio */
    width: 100% !important;
    height: auto !important;
    /* Flexible dimensions that adapt to image content on mobile */
    max-width: 100% !important;
    min-height: 160px !important; /* Minimum height for mobile */
    max-height: 260px !important; /* Maximum height for mobile */
    border-radius: 0.75rem !important;
    object-fit: cover !important; /* Fill container without letterboxing on mobile */
    object-position: center !important;
    /* Optimized for mobile performance */
    filter: brightness(0.95) contrast(1.05) saturate(1.05) !important;
    /* Force hardware acceleration for smooth zoom-independent rendering */
    transform: translateZ(0) !important;
    will-change: transform, filter !important;
    /* Additional zoom-independent properties for mobile */
    contain: layout style paint !important;
    /* Optimize image rendering for consistent display */
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    /* Ensure proper scaling behavior */
    display: block !important;
  }

  .suz-service-image:hover {
    transform: translateZ(0) !important; /* Maintain hardware acceleration on mobile */
    filter: brightness(1) contrast(1.1) saturate(1.1) !important;
  }

  .suz-service-image-container:hover {
    transform: translateY(-2px) !important; /* Reduced movement for mobile */
  }

  /* Enhanced mobile image container adaptation for better photo frame fitting */
  .suz-service-image-container {
    /* Use aspect-ratio to maintain better proportions */
    aspect-ratio: 4/3 !important; /* Standard aspect ratio for service images */
    height: auto !important; /* Override fixed height for better adaptation */
    min-height: 180px !important;
    max-height: 280px !important;
    /* Ensure container adapts to content */
    align-items: stretch !important;
    padding: 8px !important; /* Add padding to create better frame effect */
  }

  /* Ensure images fill the adaptive container properly */
  .suz-service-image {
    width: 100% !important;
    height: 100% !important; /* Fill the aspect-ratio container */
    object-fit: cover !important;
    min-height: unset !important; /* Remove conflicting min-height */
    border-radius: 0.5rem !important; /* Slightly smaller radius inside padded container */
  }
}

/* Fallback for browsers that don't support aspect-ratio */
@supports not (aspect-ratio: 4/3) {
  @media (max-width: 768px) {
    .suz-service-image-container {
      height: 220px !important; /* Fallback fixed height */
      aspect-ratio: unset !important;
    }

    .suz-service-image {
      height: auto !important;
      max-height: 200px !important;
    }
  }
}

/* Extra small screens (320px and below) */
@media (max-width: 320px) {
  .suz-services-section .suz-icon-badge-premium {
    width: 60px !important;
    height: 60px !important;
    /* Maximum contrast for very small screens */
    background: linear-gradient(135deg, rgba(10, 132, 255, 0.4), rgba(100, 210, 255, 0.3)) !important;
    border: 1px solid rgba(10, 132, 255, 0.5) !important;
  }

  .suz-services-section .suz-icon-badge-premium svg {
    width: 26px !important;
    height: 26px !important;
  }
}

/* Tablet and medium screens with zoom-independent service images */
@media (min-width: 769px) and (max-width: 1024px) {
  .suz-company-card {
    min-width: 280px;
    max-width: 320px;
  }

  .animate-scroll-right {
    animation-duration: 50s; /* Medium speed for tablets */
    /* Ensure consistent animation on tablets */
    animation-play-state: running;
  }

  .suz-company-scroll {
    transform: translate3d(0, 0, 0);
  }

  /* Service images tablet optimization with true zoom-independent sizing */
  .suz-service-image-container {
    /* Fixed pixel dimensions that remain constant regardless of browser zoom on tablets */
    height: 250px !important; /* Fixed height for tablets */
    /* Additional zoom-independent properties for tablets */
    contain: layout style paint !important;
    /* Force hardware acceleration for smooth zoom-independent rendering */
    transform: translateZ(0) !important;
    /* Ensure proper flexbox layout */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .suz-service-image {
    /* True zoom-independent image sizing for tablets */
    width: auto !important;
    height: auto !important;
    max-width: 100% !important;
    max-height: 230px !important; /* Fixed max height for tablets */
    /* Additional zoom-independent properties for tablets */
    contain: layout style paint !important;
    /* Optimize image rendering for consistent display */
    image-rendering: -webkit-optimize-contrast !important;
    image-rendering: crisp-edges !important;
    /* Ensure proper scaling behavior */
    display: block !important;
    object-fit: cover !important;
    object-position: center !important;
  }
}

/* Large screens optimization */
@media (min-width: 1025px) {
  .animate-scroll-right {
    animation-duration: 60s; /* Original speed for desktop */
  }
}

/* Fallback for browsers with poor animation support */
@supports not (animation: scroll-right 60s linear infinite) {
  .suz-company-scroll {
    overflow-x: auto;
    scroll-behavior: smooth;
  }

  .animate-scroll-right {
    animation: none;
  }
}

/* Force animation on supported browsers */
@supports (animation: scroll-right 60s linear infinite) {
  .animate-scroll-right {
    animation-play-state: running;
  }
}

/* ===== CROSS-BROWSER MOBILE COMPATIBILITY FIXES ===== */

/* iOS Safari specific fixes */
@supports (-webkit-backdrop-filter: blur(10px)) and (not (backdrop-filter: blur(10px))) {
  .suz-company-showcase .suz-card-glass {
    /* iOS Safari specific backdrop filter */
    -webkit-backdrop-filter: blur(15px) !important;
    background: rgba(28, 28, 30, 0.92) !important;
    /* Enhanced iOS performance */
    -webkit-transform: translateZ(0);
    -webkit-backface-visibility: hidden;
  }

  .animate-scroll-right {
    /* iOS Safari animation optimization */
    -webkit-animation-play-state: running !important;
    -webkit-transform: translate3d(0, 0, 0);
  }
}

/* Chrome Mobile specific optimizations */
@media screen and (-webkit-min-device-pixel-ratio: 1) and (max-width: 768px) {
  .suz-company-showcase .suz-card-glass {
    /* Chrome Mobile glass effects */
    -webkit-backdrop-filter: blur(20px) !important;
    backdrop-filter: blur(20px) !important;
    background: rgba(28, 28, 30, 0.88) !important;
  }

  .animate-scroll-right {
    /* Chrome Mobile animation optimization */
    will-change: transform;
    contain: layout style paint;
  }
}

/* Firefox Mobile specific fixes */
@-moz-document url-prefix() {
  @media (max-width: 768px) {
    .suz-company-showcase .suz-card-glass {
      /* Firefox Mobile fallback - no backdrop-filter support */
      background: rgba(28, 28, 30, 0.98) !important;
      background-image: linear-gradient(135deg,
        rgba(28, 28, 30, 0.98) 0%,
        rgba(44, 44, 46, 0.95) 50%,
        rgba(28, 28, 30, 0.98) 100%) !important;
      /* Enhanced border for Firefox */
      border: 1px solid rgba(255, 255, 255, 0.35) !important;
    }

    .animate-scroll-right {
      /* Firefox Mobile animation optimization */
      transform: translateZ(0);
      backface-visibility: hidden;
    }

    /* Firefox Mobile services icon fallback */
    .suz-services-section .suz-icon-badge-premium {
      background: rgba(28, 28, 30, 0.95) !important;
      background-image: linear-gradient(135deg,
        rgba(10, 132, 255, 0.4) 0%,
        rgba(100, 210, 255, 0.3) 50%,
        rgba(10, 132, 255, 0.4) 100%) !important;
      border: 1px solid rgba(10, 132, 255, 0.5) !important;
    }
  }
}

/* Edge Mobile specific fixes */
@supports (-ms-ime-align: auto) {
  @media (max-width: 768px) {
    .suz-company-showcase .suz-card-glass {
      /* Edge Mobile fallback */
      background: rgba(28, 28, 30, 0.95) !important;
      filter: blur(0.5px); /* Subtle blur effect for Edge */
    }

    /* Edge Mobile services icon fallback */
    .suz-services-section .suz-icon-badge-premium {
      background: rgba(28, 28, 30, 0.9) !important;
      background-image: linear-gradient(135deg,
        rgba(10, 132, 255, 0.35) 0%,
        rgba(100, 210, 255, 0.25) 100%) !important;
      filter: blur(0.3px); /* Subtle blur for glass effect */
    }
  }
}

/* Enhanced premium glass morphism for company cards */
.suz-company-showcase .suz-card-glass {
  /* Base background for browsers without backdrop-filter support */
  background: rgba(28, 28, 30, 0.95) !important;

  /* Enhanced glass morphism with progressive enhancement */
  -webkit-backdrop-filter: blur(25px) !important;
  backdrop-filter: blur(25px) !important;

  /* Fallback gradient for browsers without backdrop-filter */
  background-image: linear-gradient(135deg,
    rgba(28, 28, 30, 0.95) 0%,
    rgba(44, 44, 46, 0.9) 25%,
    rgba(58, 58, 60, 0.95) 50%,
    rgba(44, 44, 46, 0.9) 75%,
    rgba(28, 28, 30, 0.95) 100%) !important;

  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  box-shadow:
    0 20px 40px -10px rgba(0, 0, 0, 0.7),
    0 10px 25px -5px rgba(10, 132, 255, 0.15),
    0 2px 8px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Enhanced glass morphism for browsers with backdrop-filter support */
@supports (backdrop-filter: blur(25px)) or (-webkit-backdrop-filter: blur(25px)) {
  .suz-company-showcase .suz-card-glass {
    background: rgba(28, 28, 30, 0.85) !important;
    background-image: none !important;
  }
}

.suz-company-showcase .suz-card-glass:hover {
  background: rgba(44, 44, 46, 0.95) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow:
    0 25px 50px -10px rgba(0, 0, 0, 0.7),
    0 15px 30px -5px rgba(10, 132, 255, 0.2),
    0 5px 15px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-5px) scale(1.02) !important;
}

.suz-company-showcase .suz-card-glass:hover {
  background: rgba(58, 58, 60, 0.9) !important;
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.6),
    0 4px 12px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(10, 132, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* Enhanced Button Interactions */
.premium-button-enhanced {
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.premium-button-enhanced::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.premium-button-enhanced:hover::before {
  left: 100%;
}

.premium-button-enhanced:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 20px 40px -10px rgba(59, 130, 246, 0.4);
}

/* Smooth Card Hover Effects */
.card-hover-enhanced {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
}

.card-hover-enhanced:hover {
  transform: translateY(-8px) rotateX(2deg);
  box-shadow:
    0 25px 50px -10px rgba(0, 0, 0, 0.15),
    0 10px 30px -5px rgba(59, 130, 246, 0.1);
}

/* Text Reveal Animation */
@keyframes text-reveal {
  from {
    opacity: 0;
    transform: translateY(20px);
    filter: blur(4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    filter: blur(0);
  }
}

.text-reveal {
  animation: text-reveal 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Smooth Scrolling */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Accessibility: Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  /* Keep essential focus transitions */
  *:focus {
    transition-duration: 0.1s !important;
  }
}

/* Enhanced Custom Scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(6, 182, 212, 0.4));
  border-radius: 5px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.6), rgba(6, 182, 212, 0.6));
}

/* Mobile Responsiveness Enhancements */
@media (max-width: 768px) {
  .glass-morphism-premium {
    /* Enhanced mobile glass morphism with fallbacks */
    background: rgba(28, 28, 30, 0.95) !important;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    /* Fallback for browsers without backdrop-filter support */
    background-image: linear-gradient(135deg,
      rgba(28, 28, 30, 0.95) 0%,
      rgba(44, 44, 46, 0.9) 50%,
      rgba(28, 28, 30, 0.95) 100%);
  }

  /* Enhanced mobile company showcase glass effects */
  .suz-company-showcase .suz-card-glass {
    /* Stronger background for mobile readability */
    background: rgba(28, 28, 30, 0.98) !important;

    /* Mobile-optimized glass morphism */
    -webkit-backdrop-filter: blur(15px) !important;
    backdrop-filter: blur(15px) !important;

    /* Enhanced fallback for mobile browsers without backdrop-filter */
    background-image: linear-gradient(135deg,
      rgba(28, 28, 30, 0.98) 0%,
      rgba(44, 44, 46, 0.95) 25%,
      rgba(58, 58, 60, 0.98) 50%,
      rgba(44, 44, 46, 0.95) 75%,
      rgba(28, 28, 30, 0.98) 100%) !important;

    /* Enhanced border visibility on mobile */
    border: 1px solid rgba(255, 255, 255, 0.3) !important;

    /* Mobile-optimized shadows with better contrast */
    box-shadow:
      0 15px 35px -5px rgba(0, 0, 0, 0.8),
      0 8px 20px -3px rgba(10, 132, 255, 0.2),
      0 2px 8px rgba(0, 0, 0, 0.5),
      inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;

    /* Optimize for mobile performance */
    will-change: transform, opacity;
    contain: layout style paint;
  }

  /* Mobile-specific backdrop-filter support detection */
  @supports (backdrop-filter: blur(15px)) or (-webkit-backdrop-filter: blur(15px)) {
    .suz-company-showcase .suz-card-glass {
      background: rgba(28, 28, 30, 0.9) !important;
      background-image: none !important;
    }
  }

  /* iOS Safari specific optimizations */
  @supports (-webkit-backdrop-filter: blur(15px)) and (not (backdrop-filter: blur(15px))) {
    .suz-company-showcase .suz-card-glass {
      -webkit-backdrop-filter: blur(12px) !important;
      background: rgba(28, 28, 30, 0.92) !important;
    }
  }
}

  .logo-glow img {
    min-width: 56px !important;
    min-height: 56px !important;
  }

  .service-card-premium {
    margin-bottom: 1rem;
  }

  .team-card {
    margin-bottom: 1rem;
  }

  /* Enhanced mobile typography */
  .suz-text-display-xl {
    font-size: 2.5rem !important;
    line-height: 1.1 !important;
  }

  .suz-text-display-lg {
    font-size: 2rem !important;
    line-height: 1.2 !important;
  }

  .suz-text-heading-xl {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
  }

  /* Enhanced mobile company showcase typography */
  .suz-company-showcase h2 {
    font-size: clamp(1.75rem, 5vw, 2.5rem) !important;
    line-height: 1.2 !important;
    margin-bottom: 1.5rem !important;
  }

  .suz-company-showcase p {
    font-size: clamp(1rem, 3vw, 1.25rem) !important;
    line-height: 1.5 !important;
  }

  /* Mobile company card content improvements */
  .suz-company-card-content {
    padding: 1.25rem !important;
  }

  .suz-company-card-content h3 {
    font-size: clamp(0.875rem, 2.5vw, 1rem) !important;
    line-height: 1.3 !important;
    margin-bottom: 0.75rem !important;
    /* Enhanced German text readability */
    font-weight: 600 !important;
    color: #f1f5f9 !important;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.6) !important;
  }

  /* Enhanced mobile navigation improvements - removed conflicting rules for new mobile nav structure */

  /* Mobile button improvements */
  .suz-btn-primary {
    padding: var(--space-4) var(--space-8) !important;
    font-size: 1rem !important;
    min-height: 48px; /* Touch target size */
  }

  /* Mobile spacing adjustments */
  section {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}

/* Tablet responsiveness */
@media (min-width: 769px) and (max-width: 1024px) {
  .suz-text-display-xl {
    font-size: 3.5rem;
  }

  .grid.lg\\:grid-cols-3 {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .grid.lg\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Performance Optimization Classes */
.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.lazy-load.loaded {
  opacity: 1;
}

.image-optimized {
  width: 100%;
  height: auto;
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Preload critical resources */
.critical-resource {
  font-display: swap;
}

/* Apple-Inspired Component Classes - High Priority */

/* Button System */
.suz-btn-primary {
  background: var(--suz-blue-primary) !important;
  color: white !important;
  padding: var(--space-3) var(--space-6) !important;
  border-radius: var(--radius-lg) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--text-body-lg) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all var(--duration-normal) var(--ease-out-cubic) !important;
  border: none !important;
  cursor: pointer !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  text-decoration: none !important;
}

.suz-btn-primary:hover {
  background: #0056CC !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.suz-btn-secondary {
  background: var(--suz-surface-primary);
  color: var(--suz-blue-primary);
  border: 1px solid var(--suz-gray-200);
  padding: var(--space-3) var(--space-6);
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-body-lg);
  transition: all var(--duration-normal) var(--ease-out-cubic);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.suz-btn-secondary:hover {
  background: var(--suz-gray-50);
  border-color: var(--suz-blue-primary);
  transform: translateY(-1px);
}

/* Card System */
.suz-card-primary {
  background: var(--suz-surface-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  padding: var(--space-6);
  border: 1px solid var(--suz-gray-100);
  transition: all var(--duration-normal) var(--ease-out-cubic);
}

.suz-card-primary:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.suz-card-glass {
  background: var(--suz-surface-glass) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: var(--radius-xl) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: var(--shadow-lg) !important;
  padding: var(--space-6) !important;
  /* Performance optimization for glass effects */
  will-change: transform, opacity;
  contain: layout style paint;
  /* Enhanced fallback for browsers without backdrop-filter support */
  background-image: linear-gradient(135deg,
    rgba(28, 28, 30, 0.85) 0%,
    rgba(44, 44, 46, 0.8) 50%,
    rgba(28, 28, 30, 0.85) 100%) !important;
  /* Smooth transitions for all glass effects */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Progressive enhancement for suz-card-glass with backdrop-filter support */
@supports (backdrop-filter: blur(20px)) or (-webkit-backdrop-filter: blur(20px)) {
  .suz-card-glass {
    background: var(--suz-surface-glass) !important;
    background-image: none !important;
  }
}

/* Navigation System */
.suz-nav-primary {
  background: rgba(28, 28, 30, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--suz-gray-100);
  padding: var(--space-4) 0;
}

/* Enhanced Navigation Centering - Only applies to desktop navigation */
nav[role="navigation"]:not([aria-label="Mobile Navigation"]) {
  /* Ensure perfect horizontal centering for desktop nav */
  position: fixed !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  /* Prevent layout shifts */
  will-change: transform;
  /* Ensure proper stacking */
  z-index: 50;
  /* Ensure content doesn't overflow */
  box-sizing: border-box;
  /* Center the navigation container */
  display: block !important;
  text-align: center;
}

/* Ensure the glass container is centered within the desktop nav */
nav[role="navigation"]:not([aria-label="Mobile Navigation"]) > div {
  margin: 0 auto;
  display: inline-block;
}

/* Desktop navigation centering for large screens only */
@media (min-width: 769px) {
  nav[role="navigation"]:not([aria-label="Mobile Navigation"]) {
    /* Large screens and up - desktop navigation */
    left: 50% !important;
    transform: translateX(-50%) !important;
    max-width: none !important;
  }
}



.suz-nav-link {
  color: var(--suz-gray-600);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-body-lg);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--duration-normal) var(--ease-out-cubic);
  text-decoration: none;
  cursor: pointer;
  border: none;
  background: none;
}

.suz-nav-link:hover {
  color: var(--suz-blue-primary);
  background: var(--suz-gray-50);
  transform: scale(1.05);
}

/* Enhanced Typography Classes with German Text Optimization */
.suz-text-display-xl {
  font-size: var(--text-display-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
  /* German text optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.suz-text-display-lg {
  font-size: var(--text-display-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
  /* German text optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.suz-text-heading-xl {
  font-size: var(--text-heading-xl) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
  /* German text optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.suz-text-heading-lg {
  font-size: var(--text-heading-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-normal) !important;
  /* German text optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.suz-text-heading-md {
  font-size: var(--text-heading-md) !important;
  font-weight: var(--font-weight-semibold) !important;
  line-height: var(--line-height-normal) !important;
  /* German text optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.suz-text-body-xl {
  font-size: var(--text-body-xl) !important;
  font-weight: var(--font-weight-regular) !important;
  line-height: var(--line-height-relaxed) !important;
  /* German text optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.suz-text-body-lg {
  font-size: var(--text-body-lg) !important;
  font-weight: var(--font-weight-regular) !important;
  line-height: var(--line-height-normal) !important;
  /* German text optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.suz-text-body-md {
  font-size: var(--text-body-md) !important;
  font-weight: var(--font-weight-regular) !important;
  line-height: var(--line-height-normal) !important;
  /* German text optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Section Title Class */
.suz-section-title {
  font-size: var(--text-display-lg) !important;
  font-weight: var(--font-weight-bold) !important;
  line-height: var(--line-height-tight) !important;
  letter-spacing: -0.025em !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Standardized Section Spacing Classes */
.suz-section-standard {
  padding: var(--section-spacing-vertical) var(--space-4);
  margin: 0; /* Remove margins to prevent black strips between sections */
}

.suz-section-hero {
  padding: calc(8rem + var(--space-4)) var(--space-4) var(--section-spacing-vertical);
  margin: 0;
  min-height: 100vh;
}

.suz-section-compact {
  padding: var(--section-spacing-mobile) var(--space-4);
  margin: 0;
}

/* Mobile section spacing adjustments */
@media (max-width: 768px) {
  .suz-section-standard {
    padding: var(--section-spacing-mobile) var(--space-4);
  }

  .suz-section-hero {
    padding: calc(6rem + var(--space-4)) var(--space-4) var(--section-spacing-mobile);
  }
}

/* ===== ENHANCED HERO SECTION STYLES ===== */

/* Hero Section Container */
.suz-hero-enhanced {
  position: relative;
  background: linear-gradient(135deg,
    rgba(15, 23, 42, 0.95) 0%,
    rgba(30, 41, 59, 0.9) 50%,
    rgba(15, 23, 42, 0.95) 100%);
  overflow: hidden;
}

/* Premium Radial Gradient Background */
.bg-radial-gradient-hero {
  background: radial-gradient(
    ellipse 80% 50% at 50% 40%,
    rgba(59, 130, 246, 0.15) 0%,
    rgba(6, 182, 212, 0.1) 30%,
    rgba(139, 92, 246, 0.08) 60%,
    transparent 100%
  );
}

/* Enhanced Typography Hierarchy */
.suz-hero-headline-container {
  margin-bottom: 2rem;
  animation: hero-fade-in-up 1s ease-out 0.2s both;
}

.suz-hero-title {
  font-size: clamp(2.5rem, 8vw, 5rem) !important;
  font-weight: 800 !important;
  line-height: 1.1 !important;
  letter-spacing: -0.02em !important;
  text-shadow:
    0 4px 8px rgba(0, 0, 0, 0.3),
    0 2px 4px rgba(0, 0, 0, 0.2) !important;
  margin: 0 !important;
}

.suz-hero-accent {
  display: inline-block;
  background: linear-gradient(135deg, #3b82f6, #06b6d4, #0ea5e9, #8b5cf6, #3b82f6) !important;
  background-size: 300% 300% !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  animation: gradient-shift 4s ease-in-out infinite, pulse-glow 3s ease-in-out infinite;
}

.suz-hero-main-text {
  color: #f1f5f9 !important;
  font-weight: 700 !important;
}

/* Enhanced Subtitle */
.suz-hero-subtitle-container {
  margin-bottom: 2rem;
  animation: hero-fade-in-up 1s ease-out 0.4s both;
}

.suz-hero-subtitle {
  font-size: clamp(1.25rem, 4vw, 2rem) !important;
  font-weight: 300 !important;
  line-height: 1.4 !important;
  letter-spacing: 0.02em !important;
  color: #cbd5e1 !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
  margin: 0 !important;
}

/* Enhanced Description */
.suz-hero-description-container {
  margin-bottom: 3rem;
  animation: hero-fade-in-up 1s ease-out 0.6s both;
}

.suz-hero-description {
  font-size: clamp(1rem, 2.5vw, 1.25rem) !important;
  font-weight: 400 !important;
  line-height: 1.6 !important;
  color: #94a3b8 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
  margin: 0 !important;
}

/* Hero Animation Keyframes */
@keyframes hero-fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== ROTATING TITLE ANIMATIONS ===== */

/* Smooth fade-in animation for rotating titles */
@keyframes suz-title-fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.98);
    filter: blur(2px);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* Smooth fade-out animation for rotating titles */
@keyframes suz-title-fade-out {
  0% {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px) scale(0.98);
    filter: blur(2px);
  }
}

/* Rotating title base styles - 60fps optimized */
.suz-rotating-title {
  display: inline-block;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: opacity, transform, filter;
  /* Hardware acceleration for smooth 60fps performance */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  /* Optimize for compositing */
  contain: layout style paint;
}

/* Animation classes for rotating titles - Performance optimized */
.suz-title-fade-in {
  animation: suz-title-fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  /* Ensure smooth animation performance */
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.suz-title-fade-out {
  animation: suz-title-fade-out 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  /* Ensure smooth animation performance */
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Prevent layout shifts during animation */
.suz-hero-headline-container {
  /* Maintain consistent height during title transitions */
  min-height: 1.2em;
  position: relative;
}

/* Ensure gradient animation continues smoothly */
.suz-hero-accent.suz-rotating-title {
  /* Maintain gradient animation during title rotation */
  background-size: 300% 300% !important;
  animation: gradient-shift 4s ease-in-out infinite, pulse-glow 3s ease-in-out infinite;
}

.suz-hero-accent.suz-rotating-title.suz-title-fade-in {
  /* Combine gradient animation with fade-in */
  animation:
    gradient-shift 4s ease-in-out infinite,
    pulse-glow 3s ease-in-out infinite,
    suz-title-fade-in 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.suz-hero-accent.suz-rotating-title.suz-title-fade-out {
  /* Combine gradient animation with fade-out */
  animation:
    gradient-shift 4s ease-in-out infinite,
    pulse-glow 3s ease-in-out infinite,
    suz-title-fade-out 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .suz-rotating-title {
    animation: none !important;
    transition: none !important;
  }

  .suz-title-fade-in,
  .suz-title-fade-out {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
    filter: none !important;
  }
}

/* Enhanced CTA Button Styles */
.suz-hero-cta-container {
  animation: hero-fade-in-up 1s ease-out 0.8s both;
}

.suz-hero-cta-primary,
.suz-hero-cta-secondary {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2.5rem;
  border-radius: 9999px;
  font-size: 1.125rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  border: none;
  cursor: pointer;
  min-height: 56px;
  min-width: 200px;
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.25),
    0 4px 12px -2px rgba(0, 0, 0, 0.1);
}

.suz-hero-cta-primary {
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  color: white;
}

.suz-hero-cta-secondary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
}

.suz-hero-cta-primary:hover,
.suz-hero-cta-secondary:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
    0 20px 40px -10px rgba(0, 0, 0, 0.3),
    0 8px 20px -4px rgba(0, 0, 0, 0.15);
}

.suz-hero-cta-primary:hover {
  background: linear-gradient(135deg, #15803d 0%, #166534 100%);
}

.suz-hero-cta-secondary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
}

.suz-hero-cta-content {
  position: relative;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.3s ease;
}

.suz-hero-cta-icon {
  width: 1.25rem;
  height: 1.25rem;
  transition: transform 0.3s ease;
}

.suz-hero-cta-text {
  font-weight: 600;
  letter-spacing: 0.025em;
}

.group:hover .suz-hero-cta-content {
  transform: scale(1.05);
}

.group:hover .suz-hero-cta-icon {
  transform: rotate(12deg);
}

/* Premium Button Shine Effect */
.suz-hero-cta-primary::before,
.suz-hero-cta-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.suz-hero-cta-primary:hover::before,
.suz-hero-cta-secondary:hover::before {
  left: 100%;
}

/* Enhanced Floating Elements */
.suz-floating-element-1 {
  animation: float 8s ease-in-out infinite;
}

.suz-floating-element-2 {
  animation: float 8s ease-in-out infinite 2s;
}

.suz-floating-element-3 {
  animation: float 8s ease-in-out infinite 4s;
}

.suz-floating-element-4 {
  animation: float 8s ease-in-out infinite 1s;
}

.suz-floating-element-5 {
  animation: float 8s ease-in-out infinite 3s;
}

/* Enhanced Logo Styles */
.suz-logo-container {
  transition: all 0.3s ease;
}

/* Hero-only logo positioning */
.suz-hero-logo {
  position: absolute;
  top: 1.5rem;
  left: 1.5rem;
  z-index: 50;
}

.suz-logo-wrapper {
  padding: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background: rgba(28, 28, 30, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden; /* Prevent any overflow */
}

.suz-logo-wrapper:hover {
  transform: translateY(-2px);
  box-shadow:
    0 12px 40px rgba(0, 0, 0, 0.4),
    0 6px 20px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

.suz-logo-enhanced {
  width: 6.5rem; /* 104px - increased for better visibility */
  height: 6.5rem; /* 104px - increased for better visibility */
  min-width: 4rem; /* 64px minimum for mobile */
  min-height: 4rem; /* 64px minimum for mobile */
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
  object-fit: contain; /* Prevent cropping */
  object-position: center; /* Center the logo within container */
}

/* Responsive Logo Scaling */
@media (max-width: 768px) {
  .suz-logo-enhanced {
    width: 5.5rem; /* 88px on mobile - increased for better visibility */
    height: 5.5rem; /* 88px on mobile - increased for better visibility */
    min-width: 3.5rem; /* 56px minimum */
    min-height: 3.5rem; /* 56px minimum */
    object-fit: contain; /* Prevent cropping */
    object-position: center; /* Center the logo within container */
  }

  .suz-logo-wrapper {
    padding: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* Prevent any overflow */
  }

  /* Mobile: Center logo horizontally above title with minimal top spacing */
  .suz-hero-logo {
    top: 0.5rem !important; /* Further reduced from 1rem to bring logo even closer to title */
    left: 50% !important;
    transform: translateX(-50%) !important;
    position: absolute !important;
  }
}

@media (max-width: 480px) {
  .suz-logo-enhanced {
    width: 4.5rem; /* 72px on small mobile - increased for better visibility */
    height: 4.5rem; /* 72px on small mobile - increased for better visibility */
    object-fit: contain; /* Prevent cropping */
    object-position: center; /* Center the logo within container */
  }

  .suz-logo-wrapper {
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* Prevent any overflow */
  }

  /* Small mobile: Ensure centered positioning */
  .suz-hero-logo {
    top: 1.5rem !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
  }
}

@media (min-width: 1024px) {
  .suz-logo-enhanced {
    width: 7.5rem; /* 120px on desktop - increased for better visibility */
    height: 7.5rem; /* 120px on desktop - increased for better visibility */
    object-fit: contain; /* Prevent cropping */
    object-position: center; /* Center the logo within container */
  }

  .suz-logo-wrapper {
    padding: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden; /* Prevent any overflow */
  }
}

/* ===== ENHANCED MOBILE NAVIGATION STYLES ===== */

/* Mobile Menu Button */
.suz-mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 0.5rem;
}

.suz-mobile-menu-button:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

/* Hamburger Icon */
.suz-hamburger-icon {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 1.5rem;
  height: 1.125rem;
}

.suz-hamburger-line {
  display: block;
  width: 100%;
  height: 2px;
  background: #f1f5f9;
  border-radius: 1px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

/* Hamburger Animation States */
.suz-hamburger-line-1-open {
  transform: translateY(7px) rotate(45deg);
}

.suz-hamburger-line-2-open {
  opacity: 0;
  transform: scaleX(0);
}

.suz-hamburger-line-3-open {
  transform: translateY(-7px) rotate(-45deg);
}

/* Mobile Menu Overlay */
.suz-mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 40;
  animation: fade-in 0.3s ease-out;
}

.suz-mobile-menu-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

/* Mobile Menu */
.suz-mobile-menu {
  position: fixed;
  top: 5rem;
  right: 1.5rem;
  width: calc(100vw - 3rem);
  max-width: 20rem;
  z-index: 50;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.suz-mobile-menu-open {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.suz-mobile-menu-closed {
  opacity: 0;
  transform: translateY(-1rem);
  pointer-events: none;
}

.suz-mobile-menu-content {
  background: rgba(28, 28, 30, 0.95);
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
  padding: 1rem;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.4),
    0 8px 20px rgba(0, 0, 0, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Mobile Navigation Links */
.suz-mobile-nav-link {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  color: #f1f5f9;
  font-weight: 500;
  font-size: 1rem;
  text-align: center;
  background: none;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.suz-mobile-nav-link:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  transform: translateX(4px);
}

.suz-mobile-nav-link:active {
  transform: translateX(4px) scale(0.98);
}

/* ===== RESPONSIVE HERO ENHANCEMENTS ===== */

/* Mobile Hero Adjustments */
@media (max-width: 768px) {
  .suz-hero-enhanced {
    padding-top: 7rem !important;
  }

  .suz-hero-title {
    font-size: clamp(2rem, 10vw, 3.5rem) !important;
    margin-bottom: 1.5rem !important;
  }

  .suz-hero-subtitle {
    font-size: clamp(1rem, 5vw, 1.5rem) !important;
    margin-bottom: 1.5rem !important;
  }

  .suz-hero-description {
    font-size: clamp(0.875rem, 4vw, 1.125rem) !important;
    margin-bottom: 2rem !important;
    padding: 0 1rem;
  }

  .suz-hero-cta-container {
    padding: 0 1rem;
  }

  .suz-hero-cta-primary,
  .suz-hero-cta-secondary {
    width: 100%;
    max-width: 280px;
    padding: 0.875rem 2rem;
    font-size: 1rem;
    min-height: 52px;
  }

  .suz-hero-cta-icon {
    width: 1.125rem;
    height: 1.125rem;
  }

  /* Adjust floating elements for mobile */
  .suz-floating-element-1,
  .suz-floating-element-2,
  .suz-floating-element-3,
  .suz-floating-element-4,
  .suz-floating-element-5 {
    opacity: 0.6;
    animation-duration: 10s;
  }
}

/* Small Mobile Adjustments */
@media (max-width: 480px) {
  .suz-hero-title {
    font-size: clamp(1.75rem, 12vw, 3rem) !important;
  }

  .suz-hero-subtitle {
    font-size: clamp(0.875rem, 6vw, 1.25rem) !important;
  }

  .suz-hero-description {
    font-size: clamp(0.8rem, 4.5vw, 1rem) !important;
    line-height: 1.5 !important;
  }

  .suz-hero-cta-primary,
  .suz-hero-cta-secondary {
    padding: 0.75rem 1.5rem;
    font-size: 0.9rem;
    min-height: 48px;
  }
}

/* Tablet Adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .suz-hero-title {
    font-size: clamp(3rem, 7vw, 4.5rem) !important;
  }

  .suz-hero-subtitle {
    font-size: clamp(1.5rem, 3.5vw, 1.875rem) !important;
  }

  .suz-hero-description {
    font-size: clamp(1.125rem, 2.5vw, 1.25rem) !important;
  }
}

/* ===== ACCESSIBILITY & PERFORMANCE ENHANCEMENTS ===== */

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .suz-hero-enhanced,
  .suz-hero-headline-container,
  .suz-hero-subtitle-container,
  .suz-hero-description-container,
  .suz-hero-cta-container,
  .suz-floating-element-1,
  .suz-floating-element-2,
  .suz-floating-element-3,
  .suz-floating-element-4,
  .suz-floating-element-5,
  .floating-element,
  .gradient-text-animated,
  .pulse-glow,
  .animate-fade-in,
  .hero-fade-in-up {
    animation: none !important;
    transition: none !important;
  }

  .suz-hero-accent {
    background: #3b82f6 !important;
    -webkit-background-clip: unset !important;
    -webkit-text-fill-color: unset !important;
    background-clip: unset !important;
    color: #3b82f6 !important;
  }

  .suz-hamburger-line {
    transition: none !important;
  }

  .suz-mobile-menu {
    transition: none !important;
  }

  .suz-logo-enhanced,
  .suz-logo-wrapper,
  .suz-hero-cta-primary,
  .suz-hero-cta-secondary,
  .suz-mobile-nav-link {
    transition: none !important;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .suz-hero-title,
  .suz-hero-subtitle,
  .suz-hero-description {
    text-shadow: none !important;
  }

  .suz-card-glass,
  .suz-logo-wrapper,
  .suz-mobile-menu-content {
    background: rgba(0, 0, 0, 0.9) !important;
    border: 2px solid #ffffff !important;
  }

  .suz-hero-cta-primary,
  .suz-hero-cta-secondary {
    border: 2px solid #ffffff !important;
  }
}

/* Performance Optimizations */
.suz-hero-enhanced,
.suz-floating-element-1,
.suz-floating-element-2,
.suz-floating-element-3,
.suz-floating-element-4,
.suz-floating-element-5 {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.suz-hero-cta-primary,
.suz-hero-cta-secondary,
.suz-mobile-menu-button,
.suz-logo-wrapper {
  will-change: transform;
  transform: translateZ(0);
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus States */
.suz-focus-ring:focus {
  outline: 2px solid var(--suz-blue-primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Typography Enhancements with German Language Optimization */
h1, h2, h3, h4, h5, h6 {
  font-weight: 300;
  letter-spacing: -0.025em;
  /* German text optimization */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Improved readability for German umlauts and special characters */
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  -webkit-font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

/* German Business Name Typography */
.suz-german-business-name {
  font-weight: var(--font-weight-semibold) !important;
  letter-spacing: -0.01em !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  /* Enhanced readability for German company names */
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1 !important;
  word-break: keep-all !important;
  overflow-wrap: break-word !important;
}

/* German Service Description Typography */
.suz-german-service-text {
  line-height: var(--line-height-relaxed) !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  /* Better readability for longer German descriptions */
  font-feature-settings: "kern" 1, "liga" 1 !important;
  hyphens: auto !important;
  -webkit-hyphens: auto !important;
  -ms-hyphens: auto !important;
}

/* German Testimonial Text Typography */
.suz-german-testimonial-text {
  font-style: italic !important;
  line-height: var(--line-height-relaxed) !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  /* Enhanced readability for German testimonial quotes */
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1 !important;
  /* quotes: German quotation marks - removed due to parsing issues */
}

/* Core Web Vitals Optimizations */

/* Optimize Largest Contentful Paint (LCP) */
.optimize-lcp {
  font-display: swap;
  contain: layout style paint;
  will-change: auto;
}

/* Reduce Cumulative Layout Shift (CLS) */
.optimize-cls {
  aspect-ratio: 16/9;
  width: 100%;
  height: auto;
}

/* Improve First Input Delay (FID) */
.optimize-fid {
  touch-action: manipulation;
  -webkit-user-select: none;
  user-select: none;
}

/* Critical above-the-fold content */
.critical-content {
  contain: layout style paint;
  will-change: auto;
}

/* Non-critical content that can be deferred */
.defer-content {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* Optimize animations for performance - Enhanced 60fps optimization */
.performance-animation {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  /* Additional performance optimizations */
  will-change: transform;
  contain: layout style paint;
  /* Ensure smooth compositing */
  isolation: isolate;
  /* Optimize for GPU acceleration */
  transform-style: preserve-3d;
}

/* Enhanced animation performance for all interactive elements */
.suz-interactive-element {
  /* Base performance optimizations */
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  contain: layout style paint;
  /* Smooth transitions */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Optimized hover states for 60fps performance */
.suz-interactive-element:hover {
  /* Use transform instead of changing layout properties */
  transform: translateZ(0) scale(1.02);
  /* Avoid expensive box-shadow changes during animation */
  filter: brightness(1.05);
}

/* Performance-optimized focus states */
.suz-interactive-element:focus {
  outline: 2px solid rgba(59, 130, 246, 0.8);
  outline-offset: 2px;
  /* Use transform for focus animations */
  transform: translateZ(0) scale(1.01);
}

/* Optimize images for Core Web Vitals */
.optimize-image {
  aspect-ratio: attr(width) / attr(height);
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Reduce layout thrashing */
.stable-layout {
  contain: layout;
  min-height: 200px;
}

/* Critical CSS for above-the-fold content */
.hero-critical {
  min-height: 100vh;
  contain: layout style paint;
  will-change: auto;
}

.hero-critical h1 {
  font-display: swap;
  contain: layout style;
}

/* Optimize button interactions */
.btn-optimized {
  touch-action: manipulation;
  will-change: transform;
  backface-visibility: hidden;
}

.btn-optimized:hover,
.btn-optimized:focus {
  transform: translateZ(0) scale(1.05);
}

/* Preload critical fonts */
@font-face {
  font-family: "Inter";
  font-display: swap;
  font-weight: 100 900;
}

/* Optimize scrolling performance */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Optimize focus states for accessibility and performance */
.focus-optimized:focus {
  outline: 2px solid var(--suz-blue-primary);
  outline-offset: 2px;
  transition: outline-offset 0.1s ease;
}

/* Screen Reader Support */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .glass-morphism,
  .glass-morphism-premium {
    background: rgba(255, 255, 255, 0.95);
    border: 2px solid #000;
  }

  .gradient-text,
  .gradient-text-animated {
    background: none;
    -webkit-background-clip: unset;
    -webkit-text-fill-color: unset;
    color: #000;
  }
}

/* Focus Management */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--suz-blue-primary);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Keyboard Navigation Enhancement */
.keyboard-nav *:focus {
  outline: 3px solid var(--suz-blue-primary);
  outline-offset: 2px;
}

/* Color Blind Friendly Patterns */
.pattern-support {
  background-image: repeating-linear-gradient(
    45deg,
    transparent,
    transparent 2px,
    rgba(0, 0, 0, 0.1) 2px,
    rgba(0, 0, 0, 0.1) 4px
  );
}

/* Dark Theme is now default - removed redundant .dark selectors */



::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.8), rgba(100, 210, 255, 0.8));
}

/* Text Colors - Dark theme is now default */
h1, h2, h3, h4, h5, h6 {
  color: var(--suz-gray-900);
}

p, span, div {
  color: var(--suz-gray-700);
}

/* Focus States - Dark theme is now default */
.suz-focus-ring:focus {
  outline: 2px solid var(--suz-blue-primary);
  outline-offset: 2px;
}

/* ===== TESTIMONIALS SECTION STYLES ===== */

/* Testimonials Section Container */
.suz-testimonials-section {
  position: relative;
  /* Use the same premium gradient as the main website background */
  background: linear-gradient(135deg,
    #000000 0%,
    #1a1a1a 25%,
    #0f172a 50%,
    #1e293b 75%,
    #0f1419 100%) !important;
  /* Seamless integration with other sections */
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* Add the same radial gradient overlay as the main bg-premium-gradient */
.suz-testimonials-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  pointer-events: none;
  z-index: 0;
}

/* Ensure content appears above the overlay */
.suz-testimonials-section > * {
  position: relative;
  z-index: 1;
}

/* Testimonial Card Styles */
.suz-testimonial-card {
  /* Prevent layout shifts during animation */
  contain: layout style paint;
  transform: translateZ(0);
  /* Enhanced glass morphism for testimonials */
  background: rgba(28, 28, 30, 0.85) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  backdrop-filter: blur(25px) !important;
  box-shadow:
    0 20px 40px -10px rgba(10, 132, 255, 0.15),
    0 10px 25px -5px rgba(100, 210, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.suz-testimonial-card:hover {
  background: rgba(44, 44, 46, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.25) !important;
  box-shadow:
    0 25px 50px -10px rgba(0, 0, 0, 0.7),
    0 15px 30px -5px rgba(10, 132, 255, 0.2),
    0 5px 15px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-5px) scale(1.02) !important;
}

/* Testimonial Icon Badge */
.suz-icon-badge-testimonial {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.2), rgba(100, 210, 255, 0.1));
  border: 1px solid rgba(10, 132, 255, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.suz-icon-badge-testimonial:hover {
  background: linear-gradient(135deg, rgba(10, 132, 255, 0.3), rgba(100, 210, 255, 0.2));
  border: 1px solid rgba(10, 132, 255, 0.5);
  box-shadow: 0 10px 20px rgba(10, 132, 255, 0.2);
}

/* Shimmer animation for testimonial cards */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out;
}

/* Enhanced fade-in animation for testimonials */
.suz-testimonials-section .animate-fade-in {
  opacity: 0;
  transform: translateY(20px);
  animation: testimonial-fade-in 0.8s ease-out forwards;
}

@keyframes testimonial-fade-in {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation delays for testimonial cards */
.suz-testimonial-card:nth-child(1) { animation-delay: 0.1s; }
.suz-testimonial-card:nth-child(2) { animation-delay: 0.2s; }
.suz-testimonial-card:nth-child(3) { animation-delay: 0.3s; }
.suz-testimonial-card:nth-child(4) { animation-delay: 0.4s; }
.suz-testimonial-card:nth-child(5) { animation-delay: 0.5s; }
.suz-testimonial-card:nth-child(6) { animation-delay: 0.6s; }

/* Enhanced Mobile Responsiveness for Testimonials */
@media (max-width: 768px) {
  .suz-testimonials-section {
    /* Maintain the same premium gradient background on mobile */
    background: linear-gradient(135deg,
      #000000 0%,
      #1a1a1a 25%,
      #0f172a 50%,
      #1e293b 75%,
      #0f1419 100%) !important;
    padding: var(--space-16) 0 !important;
    /* Remove margins that create black strips between sections */
    margin: 0 !important;
    /* Ensure proper overflow handling on mobile */
    overflow-x: hidden;
    /* Optimize for mobile performance */
    contain: layout style;
  }

  .suz-testimonial-card {
    /* Stronger background for mobile readability */
    background: rgba(28, 28, 30, 0.95) !important;
    /* Mobile-optimized glass morphism */
    -webkit-backdrop-filter: blur(15px) !important;
    backdrop-filter: blur(15px) !important;
    /* Enhanced fallback for mobile browsers without backdrop-filter */
    background-image: linear-gradient(135deg,
      rgba(28, 28, 30, 0.95) 0%,
      rgba(44, 44, 46, 0.9) 25%,
      rgba(58, 58, 60, 0.95) 50%,
      rgba(44, 44, 46, 0.9) 75%,
      rgba(28, 28, 30, 0.95) 100%) !important;
    /* Mobile performance optimizations */
    will-change: transform;
    contain: layout style paint;
  }

  /* Mobile-specific backdrop-filter support detection */
  @supports (backdrop-filter: blur(15px)) or (-webkit-backdrop-filter: blur(15px)) {
    .suz-testimonial-card {
      background: rgba(28, 28, 30, 0.9) !important;
      background-image: none !important;
    }
  }

  /* Disable hover effects on mobile */
  .suz-testimonial-card:hover {
    transform: none !important;
    background: rgba(28, 28, 30, 0.95) !important;
  }

  /* Mobile-specific touch feedback */
  .suz-testimonial-card:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }

  /* Adjust icon size for mobile */
  .suz-icon-badge-testimonial {
    width: 40px;
    height: 40px;
  }

  /* Enhanced mobile typography for testimonials */
  .suz-testimonials-section h2 {
    font-size: clamp(1.75rem, 5vw, 2.5rem) !important;
    line-height: 1.2 !important;
    margin-bottom: 1.5rem !important;
  }

  .suz-testimonials-section p {
    font-size: clamp(1rem, 3vw, 1.25rem) !important;
    line-height: 1.5 !important;
  }
}

/* Small mobile devices */
@media (max-width: 480px) {
  .suz-testimonials-section {
    /* Maintain the same premium gradient background on small mobile */
    background: linear-gradient(135deg,
      #000000 0%,
      #1a1a1a 25%,
      #0f172a 50%,
      #1e293b 75%,
      #0f1419 100%) !important;
    overflow-x: hidden !important;
    /* Reduce padding for small screens */
    padding: var(--space-12) 0 !important;
  }

  .suz-testimonial-card {
    /* Adjust padding for small screens */
    padding: var(--space-4) !important;
  }

  /* Improve icon visibility on small screens */
  .suz-icon-badge-testimonial {
    width: 36px !important;
    height: 36px !important;
    /* Enhanced contrast for mobile */
    background: linear-gradient(135deg, rgba(10, 132, 255, 0.3), rgba(100, 210, 255, 0.2)) !important;
    border: 1px solid rgba(10, 132, 255, 0.4) !important;
  }

  .suz-icon-badge-testimonial svg {
    width: 20px !important;
    height: 20px !important;
  }
}

/* Accessibility enhancements for testimonials */
.suz-testimonial-card:focus-within,
.suz-testimonial-card:focus {
  outline: 3px solid #3b82f6 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.2) !important;
}

/* Reduced motion support for testimonials */
@media (prefers-reduced-motion: reduce) {
  .suz-testimonial-card,
  .suz-icon-badge-testimonial {
    transition: none !important;
    animation: none !important;
  }

  .suz-testimonial-card:hover {
    transform: none !important;
  }

  .animate-shimmer {
    animation: none !important;
  }
}

/* High contrast mode support for testimonials */
@media (prefers-contrast: high) {
  .suz-testimonial-card {
    background: rgba(0, 0, 0, 0.9) !important;
    border: 2px solid #ffffff !important;
  }

  .suz-icon-badge-testimonial {
    background: rgba(255, 255, 255, 0.9) !important;
    border: 2px solid #000000 !important;
  }
}

/* End of @layer components */

/* Additional utility overrides */
@layer utilities {
  /* force-apple-design already defined above */
}
