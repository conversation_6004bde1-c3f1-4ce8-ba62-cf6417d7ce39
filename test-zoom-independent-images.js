/**
 * Zoom-Independent Service Images Testing Script
 * Run this in the browser console to verify images maintain consistent visual size across zoom levels
 */

function testZoomIndependentImages() {
  console.log('🔍 Testing Zoom-Independent Service Images');
  console.log('=' .repeat(60));

  // Get all service images and containers
  const serviceImages = document.querySelectorAll('.suz-service-image');
  const serviceContainers = document.querySelectorAll('.suz-service-image-container');

  if (serviceImages.length === 0) {
    console.error('❌ No service images found. Make sure you\'re on the services section.');
    return;
  }

  console.log(`📊 Found ${serviceImages.length} service images to test`);
  console.log('');

  // Test current zoom level
  const currentZoom = Math.round(window.devicePixelRatio * 100);
  console.log(`🔍 Current browser zoom level: ${currentZoom}%`);
  console.log('');

  // Measure current dimensions
  const measurements = [];
  
  serviceImages.forEach((img, index) => {
    const container = serviceContainers[index];
    const imgRect = img.getBoundingClientRect();
    const containerRect = container.getBoundingClientRect();
    
    const measurement = {
      index: index + 1,
      image: {
        width: Math.round(imgRect.width),
        height: Math.round(imgRect.height),
        visualSize: Math.round(imgRect.width * imgRect.height)
      },
      container: {
        width: Math.round(containerRect.width),
        height: Math.round(containerRect.height),
        visualSize: Math.round(containerRect.width * containerRect.height)
      },
      computedStyles: {
        imageMinHeight: getComputedStyle(img).minHeight,
        imageMaxHeight: getComputedStyle(img).maxHeight,
        containerMinHeight: getComputedStyle(container).minHeight,
        containerMaxHeight: getComputedStyle(container).maxHeight
      }
    };
    
    measurements.push(measurement);
    
    console.log(`📸 Service Image ${index + 1}:`);
    console.log(`   Image: ${measurement.image.width}×${measurement.image.height}px (${measurement.image.visualSize} total pixels)`);
    console.log(`   Container: ${measurement.container.width}×${measurement.container.height}px`);
    console.log(`   Min Height: ${measurement.computedStyles.imageMinHeight}`);
    console.log(`   Max Height: ${measurement.computedStyles.imageMaxHeight}`);
    console.log('');
  });

  // Store measurements for comparison
  window.zoomTestData = window.zoomTestData || {};
  window.zoomTestData[currentZoom] = measurements;

  // If we have measurements from different zoom levels, compare them
  const zoomLevels = Object.keys(window.zoomTestData).map(Number).sort((a, b) => a - b);
  
  if (zoomLevels.length > 1) {
    console.log('📊 ZOOM COMPARISON ANALYSIS:');
    console.log('=' .repeat(40));
    
    const baseZoom = zoomLevels[0];
    const baseMeasurements = window.zoomTestData[baseZoom];
    
    zoomLevels.forEach(zoom => {
      if (zoom === baseZoom) return;
      
      const currentMeasurements = window.zoomTestData[zoom];
      console.log(`\n🔍 Comparing ${baseZoom}% vs ${zoom}%:`);
      
      let allConsistent = true;
      
      baseMeasurements.forEach((baseMeasurement, index) => {
        const currentMeasurement = currentMeasurements[index];
        
        const widthDiff = Math.abs(baseMeasurement.image.width - currentMeasurement.image.width);
        const heightDiff = Math.abs(baseMeasurement.image.height - currentMeasurement.image.height);
        const sizeDiff = Math.abs(baseMeasurement.image.visualSize - currentMeasurement.image.visualSize);
        
        const isConsistent = widthDiff <= 2 && heightDiff <= 2; // Allow 2px tolerance
        
        if (!isConsistent) {
          allConsistent = false;
          console.log(`   ❌ Image ${index + 1}: Size changed significantly`);
          console.log(`      ${baseZoom}%: ${baseMeasurement.image.width}×${baseMeasurement.image.height}px`);
          console.log(`      ${zoom}%: ${currentMeasurement.image.width}×${currentMeasurement.image.height}px`);
          console.log(`      Difference: ${widthDiff}×${heightDiff}px`);
        } else {
          console.log(`   ✅ Image ${index + 1}: Size consistent (±${Math.max(widthDiff, heightDiff)}px)`);
        }
      });
      
      if (allConsistent) {
        console.log(`   🎉 All images maintain consistent size between ${baseZoom}% and ${zoom}%!`);
      }
    });
  }

  // Test viewport unit calculations
  console.log('\n🧮 VIEWPORT UNIT ANALYSIS:');
  console.log('=' .repeat(30));
  
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;
  
  console.log(`Viewport: ${viewportWidth}×${viewportHeight}px`);
  
  // Calculate expected sizes based on viewport units
  const expectedDesktopMinHeight = Math.round(viewportWidth * 0.16); // 16vw
  const expectedDesktopMaxHeight = Math.round(viewportWidth * 0.20); // 20vw
  
  console.log(`Expected desktop min-height (16vw): ${expectedDesktopMinHeight}px`);
  console.log(`Expected desktop max-height (20vw): ${expectedDesktopMaxHeight}px`);
  
  if (viewportWidth <= 768) {
    const expectedMobileMinHeight = Math.round(viewportWidth * 0.25); // 25vw
    const expectedMobileMaxHeight = Math.round(viewportWidth * 0.30); // 30vw
    console.log(`Expected mobile min-height (25vw): ${expectedMobileMinHeight}px`);
    console.log(`Expected mobile max-height (30vw): ${expectedMobileMaxHeight}px`);
  }

  return {
    zoomLevel: currentZoom,
    measurements: measurements,
    viewportSize: { width: viewportWidth, height: viewportHeight },
    allData: window.zoomTestData
  };
}

// Instructions for manual testing
function showZoomTestInstructions() {
  console.log('🧪 ZOOM-INDEPENDENT TESTING INSTRUCTIONS:');
  console.log('=' .repeat(50));
  console.log('1. Run testZoomIndependentImages() at 100% zoom');
  console.log('2. Change browser zoom to 75% (Ctrl/Cmd + -)');
  console.log('3. Run testZoomIndependentImages() again');
  console.log('4. Change browser zoom to 125% (Ctrl/Cmd + +)');
  console.log('5. Run testZoomIndependentImages() again');
  console.log('6. Change browser zoom to 150%');
  console.log('7. Run testZoomIndependentImages() again');
  console.log('8. Review the comparison analysis');
  console.log('');
  console.log('✅ SUCCESS: Images should maintain consistent pixel dimensions');
  console.log('❌ FAILURE: Images scale proportionally with zoom');
  console.log('');
  console.log('To clear test data: delete window.zoomTestData');
}

// Enhanced cross-browser testing function
function testCrossBrowserZoomSupport() {
  console.log('🌐 Cross-Browser Zoom Support Test');
  console.log('=' .repeat(40));

  const testElement = document.querySelector('.suz-service-image');
  if (!testElement) {
    console.error('❌ No service images found for testing');
    return;
  }

  const computedStyle = getComputedStyle(testElement);

  // Test clamp() support
  const supportsClamp = CSS.supports('min-height', 'clamp(200px, 16vw, 320px)');
  console.log(`clamp() support: ${supportsClamp ? '✅' : '❌'}`);

  // Test contain property support
  const supportsContain = CSS.supports('contain', 'layout style paint');
  console.log(`contain property support: ${supportsContain ? '✅' : '❌'}`);

  // Test backdrop-filter support
  const supportsBackdropFilter = CSS.supports('backdrop-filter', 'blur(10px)') ||
                                  CSS.supports('-webkit-backdrop-filter', 'blur(10px)');
  console.log(`backdrop-filter support: ${supportsBackdropFilter ? '✅' : '❌'}`);

  // Test image-rendering support
  const supportsImageRendering = CSS.supports('image-rendering', 'crisp-edges') ||
                                  CSS.supports('image-rendering', '-webkit-optimize-contrast');
  console.log(`image-rendering support: ${supportsImageRendering ? '✅' : '❌'}`);

  // Check actual computed values
  console.log('\n📊 Computed Style Values:');
  console.log(`Min Height: ${computedStyle.minHeight}`);
  console.log(`Max Height: ${computedStyle.maxHeight}`);
  console.log(`Transform: ${computedStyle.transform}`);
  console.log(`Contain: ${computedStyle.contain}`);

  return {
    clamp: supportsClamp,
    contain: supportsContain,
    backdropFilter: supportsBackdropFilter,
    imageRendering: supportsImageRendering,
    computedValues: {
      minHeight: computedStyle.minHeight,
      maxHeight: computedStyle.maxHeight,
      transform: computedStyle.transform,
      contain: computedStyle.contain
    }
  };
}

// Performance testing for zoom-independent rendering
function testZoomPerformance() {
  console.log('⚡ Zoom Performance Test');
  console.log('=' .repeat(30));

  const serviceImages = document.querySelectorAll('.suz-service-image');
  if (serviceImages.length === 0) {
    console.error('❌ No service images found');
    return;
  }

  // Test rendering performance
  const startTime = performance.now();

  serviceImages.forEach((img, index) => {
    // Force reflow to test performance
    img.offsetHeight;
    img.offsetWidth;
  });

  const endTime = performance.now();
  const renderTime = endTime - startTime;

  console.log(`📊 Render time for ${serviceImages.length} images: ${renderTime.toFixed(2)}ms`);
  console.log(`📊 Average per image: ${(renderTime / serviceImages.length).toFixed(2)}ms`);

  // Test hardware acceleration
  serviceImages.forEach((img, index) => {
    const computedStyle = getComputedStyle(img);
    const hasHardwareAcceleration = computedStyle.transform !== 'none' ||
                                   computedStyle.willChange.includes('transform');
    console.log(`Image ${index + 1} hardware acceleration: ${hasHardwareAcceleration ? '✅' : '❌'}`);
  });

  return {
    totalRenderTime: renderTime,
    averagePerImage: renderTime / serviceImages.length,
    imageCount: serviceImages.length
  };
}

// Auto-run instructions
console.log('🔍 Enhanced Zoom-Independent Service Images Test Loaded');
console.log('Available functions:');
console.log('- showZoomTestInstructions() - Testing guide');
console.log('- testZoomIndependentImages() - Test current zoom level');
console.log('- testCrossBrowserZoomSupport() - Check browser support');
console.log('- testZoomPerformance() - Performance testing');
