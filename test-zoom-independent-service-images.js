/**
 * Comprehensive Service Image Zoom Independence Testing Script
 * Run this in the browser console to verify zoom-independent behavior
 */

function testZoomIndependentServiceImages() {
  console.log('🔍 Testing Zoom-Independent Service Images Implementation');
  console.log('=' .repeat(70));

  // Get all service images and containers
  const serviceImages = document.querySelectorAll('.suz-service-image');
  const serviceContainers = document.querySelectorAll('.suz-service-image-container');

  if (serviceImages.length === 0) {
    console.error('❌ No service images found. Make sure you\'re on the services section.');
    return;
  }

  console.log(`📊 Found ${serviceImages.length} service images to test`);
  console.log('');

  // Test data storage
  const testResults = {
    zoomLevels: [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
    measurements: {},
    issues: []
  };

  // Function to measure image dimensions
  function measureImages(zoomLevel) {
    const measurements = [];
    
    serviceImages.forEach((img, index) => {
      const container = serviceContainers[index];
      const imgRect = img.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      const computedStyle = getComputedStyle(img);
      const containerStyle = getComputedStyle(container);
      
      measurements.push({
        index: index + 1,
        image: {
          width: Math.round(imgRect.width),
          height: Math.round(imgRect.height),
          naturalWidth: img.naturalWidth,
          naturalHeight: img.naturalHeight
        },
        container: {
          width: Math.round(containerRect.width),
          height: Math.round(containerRect.height)
        },
        computed: {
          imageMaxWidth: computedStyle.maxWidth,
          imageMaxHeight: computedStyle.maxHeight,
          imageObjectFit: computedStyle.objectFit,
          containerHeight: containerStyle.height,
          containerDisplay: containerStyle.display
        }
      });
    });
    
    return measurements;
  }

  // Function to set zoom level
  function setZoom(level) {
    document.body.style.zoom = level;
    // Wait for layout to settle
    return new Promise(resolve => setTimeout(resolve, 100));
  }

  // Function to test at specific zoom level
  async function testAtZoomLevel(zoomLevel) {
    console.log(`🔍 Testing at ${Math.round(zoomLevel * 100)}% zoom...`);
    
    await setZoom(zoomLevel);
    const measurements = measureImages(zoomLevel);
    testResults.measurements[zoomLevel] = measurements;
    
    // Log measurements
    measurements.forEach(measurement => {
      console.log(`   📸 Image ${measurement.index}:`);
      console.log(`      Size: ${measurement.image.width}×${measurement.image.height}px`);
      console.log(`      Natural: ${measurement.image.naturalWidth}×${measurement.image.naturalHeight}px`);
      console.log(`      Container: ${measurement.container.width}×${measurement.container.height}px`);
      console.log(`      Max Height: ${measurement.computed.imageMaxHeight}`);
      console.log(`      Object Fit: ${measurement.computed.imageObjectFit}`);
    });
    console.log('');
  }

  // Function to analyze zoom consistency
  function analyzeZoomConsistency() {
    console.log('📊 ZOOM CONSISTENCY ANALYSIS:');
    console.log('=' .repeat(50));
    
    const zoomLevels = Object.keys(testResults.measurements).map(Number).sort();
    const baseZoom = 1.0;
    const baseMeasurements = testResults.measurements[baseZoom];
    
    if (!baseMeasurements) {
      console.error('❌ No baseline measurements found');
      return;
    }
    
    let allConsistent = true;
    const tolerance = 3; // 3px tolerance for rounding differences
    
    zoomLevels.forEach(zoom => {
      if (zoom === baseZoom) return;
      
      const currentMeasurements = testResults.measurements[zoom];
      console.log(`\n🔍 Comparing 100% vs ${Math.round(zoom * 100)}%:`);
      
      baseMeasurements.forEach((baseMeasurement, index) => {
        const currentMeasurement = currentMeasurements[index];
        
        // Check container consistency (should be exactly the same)
        const containerWidthDiff = Math.abs(baseMeasurement.container.width - currentMeasurement.container.width);
        const containerHeightDiff = Math.abs(baseMeasurement.container.height - currentMeasurement.container.height);
        
        // Check image consistency (should be very close)
        const imageWidthDiff = Math.abs(baseMeasurement.image.width - currentMeasurement.image.width);
        const imageHeightDiff = Math.abs(baseMeasurement.image.height - currentMeasurement.image.height);
        
        const containerConsistent = containerWidthDiff <= tolerance && containerHeightDiff <= tolerance;
        const imageConsistent = imageWidthDiff <= tolerance && imageHeightDiff <= tolerance;
        
        if (!containerConsistent || !imageConsistent) {
          allConsistent = false;
          console.log(`   ❌ Image ${index + 1}: Inconsistent sizing detected`);
          
          if (!containerConsistent) {
            console.log(`      Container: 100%=${baseMeasurement.container.width}×${baseMeasurement.container.height}px, ${Math.round(zoom * 100)}%=${currentMeasurement.container.width}×${currentMeasurement.container.height}px`);
            testResults.issues.push(`Image ${index + 1} container size changes with zoom`);
          }
          
          if (!imageConsistent) {
            console.log(`      Image: 100%=${baseMeasurement.image.width}×${baseMeasurement.image.height}px, ${Math.round(zoom * 100)}%=${currentMeasurement.image.width}×${currentMeasurement.image.height}px`);
            testResults.issues.push(`Image ${index + 1} size changes with zoom`);
          }
        } else {
          console.log(`   ✅ Image ${index + 1}: Consistent (±${Math.max(containerWidthDiff, containerHeightDiff, imageWidthDiff, imageHeightDiff)}px)`);
        }
      });
    });
    
    return allConsistent;
  }

  // Function to check for letterboxing issues
  function checkLetterboxing() {
    console.log('\n📐 LETTERBOXING ANALYSIS:');
    console.log('=' .repeat(40));
    
    const baseMeasurements = testResults.measurements[1.0];
    
    baseMeasurements.forEach((measurement, index) => {
      const { image, container } = measurement;
      const aspectRatioImage = image.naturalWidth / image.naturalHeight;
      const aspectRatioContainer = container.width / container.height;
      const aspectRatioDiff = Math.abs(aspectRatioImage - aspectRatioContainer);
      
      console.log(`📸 Image ${index + 1}:`);
      console.log(`   Natural Aspect Ratio: ${aspectRatioImage.toFixed(2)}`);
      console.log(`   Container Aspect Ratio: ${aspectRatioContainer.toFixed(2)}`);
      console.log(`   Object Fit: ${measurement.computed.imageObjectFit}`);
      
      if (measurement.computed.imageObjectFit === 'contain' && aspectRatioDiff > 0.1) {
        console.log(`   ⚠️  Potential letterboxing detected (aspect ratio mismatch: ${aspectRatioDiff.toFixed(2)})`);
        testResults.issues.push(`Image ${index + 1} may have letterboxing with object-fit: contain`);
      } else if (measurement.computed.imageObjectFit === 'cover') {
        console.log(`   ✅ Using object-fit: cover - no letterboxing`);
      } else {
        console.log(`   ℹ️  Using object-fit: ${measurement.computed.imageObjectFit}`);
      }
    });
  }

  // Function to test browser support
  function testBrowserSupport() {
    console.log('\n🌐 BROWSER SUPPORT CHECK:');
    console.log('=' .repeat(35));
    
    const features = {
      'Flexbox': CSS.supports('display', 'flex'),
      'CSS contain': CSS.supports('contain', 'layout style paint'),
      'backdrop-filter': CSS.supports('backdrop-filter', 'blur(10px)'),
      'object-fit': CSS.supports('object-fit', 'cover'),
      'transform3d': CSS.supports('transform', 'translateZ(0)')
    };
    
    Object.entries(features).forEach(([feature, supported]) => {
      console.log(`   ${supported ? '✅' : '❌'} ${feature}: ${supported ? 'Supported' : 'Not supported'}`);
      if (!supported) {
        testResults.issues.push(`Browser doesn't support ${feature}`);
      }
    });
  }

  // Main test execution
  async function runTests() {
    try {
      // Test at different zoom levels
      for (const zoomLevel of testResults.zoomLevels) {
        await testAtZoomLevel(zoomLevel);
      }
      
      // Analyze results
      const isConsistent = analyzeZoomConsistency();
      checkLetterboxing();
      testBrowserSupport();
      
      // Final summary
      console.log('\n🎯 FINAL SUMMARY:');
      console.log('=' .repeat(30));
      
      if (isConsistent && testResults.issues.length === 0) {
        console.log('🎉 SUCCESS: All service images are truly zoom-independent!');
        console.log('✅ No letterboxing issues detected');
        console.log('✅ All browser features supported');
      } else {
        console.log('⚠️  Issues detected:');
        testResults.issues.forEach(issue => {
          console.log(`   • ${issue}`);
        });
        
        if (isConsistent) {
          console.log('✅ Zoom independence: PASSED');
        } else {
          console.log('❌ Zoom independence: FAILED');
        }
      }
      
      // Reset zoom
      await setZoom(1.0);
      
    } catch (error) {
      console.error('❌ Test execution failed:', error);
    }
  }

  // Start the tests
  runTests();
}

// Auto-run if on the correct page
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    setTimeout(testZoomIndependentServiceImages, 1000);
  });
} else {
  setTimeout(testZoomIndependentServiceImages, 1000);
}

// Export for manual execution
window.testZoomIndependentServiceImages = testZoomIndependentServiceImages;

console.log('🧪 Zoom-Independent Service Images Test Script Loaded');
console.log('Run testZoomIndependentServiceImages() to start testing');
